{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9841c85070a0923ebb455065987079c512", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983a1c934fff45a0619bddf2b0a3fa855f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e092fdfcf1e022a4cb4c5f1fb7c17665", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c253cefc4a9555084b17ef8a80ae97f2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e092fdfcf1e022a4cb4c5f1fb7c17665", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db04d4161a12f31c32ebe227ab122b63", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98302cb7bdb68b1b112cd334e4c48eaf26", "guid": "bfdfe7dc352907fc980b868725387e98c04de5bfbab252b2bd9de9514dd1901d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b991c6fa0244d59f19e4de964a206d1a", "guid": "bfdfe7dc352907fc980b868725387e987c138301ae2fc71b1824e503f835ffb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb11f56834ae82310bf57bd24ff3108c", "guid": "bfdfe7dc352907fc980b868725387e9848268d18e1d8779f8246e3bcde297fda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd1a4994f1d89d4ec9693a1ec37d3577", "guid": "bfdfe7dc352907fc980b868725387e987427e3e659507ce80de9914a41630698"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98511b540551e14f4f0ee8c3decb19715b", "guid": "bfdfe7dc352907fc980b868725387e98faaae2843915b6262553b4e799b4c383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbc673adf8d862e3ecd1d5d90891f296", "guid": "bfdfe7dc352907fc980b868725387e98bc7e8276edf7e9a8b4e03141d04fd30d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de255cf41cd022dd07f8d84c34da1bb", "guid": "bfdfe7dc352907fc980b868725387e989965badb76b254f74880df3c7c819c0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980993d31df05591b0dc5a43a18a296d18", "guid": "bfdfe7dc352907fc980b868725387e987943f8f636e02e64fffc14bea17142ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2f73afc6bdf0bf56be628ee491ad67d", "guid": "bfdfe7dc352907fc980b868725387e9810bd2d55fa6283f1519cce964e561838"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98509aa05e56a5d698f7cc297ab33d5db8", "guid": "bfdfe7dc352907fc980b868725387e98808221a9e010564194adb25117cd9bfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfd955242ef0cebb563f99cd11f7ef00", "guid": "bfdfe7dc352907fc980b868725387e9813af104a45a5fe385e0913da294b4818", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846cd706e4fbca365a0fe0dbf07891f13", "guid": "bfdfe7dc352907fc980b868725387e98c6e22a4b9a4dbae27d31f02260bdd438"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98402caabad47a72f25953d665328d0b3f", "guid": "bfdfe7dc352907fc980b868725387e982d5675f66552a4d4e7a17c6cc6d7b98d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dddc8a70eb6d958c48388031ca513c1", "guid": "bfdfe7dc352907fc980b868725387e980ab1e60dd14b94b74d2ad2778c54f3b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cb164f3a72ac9973ee87afbc273be99", "guid": "bfdfe7dc352907fc980b868725387e98ba678b38c66affd5751d95c67ebbd7da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd3a8ba768e04e8a78d314fdfcbf73d", "guid": "bfdfe7dc352907fc980b868725387e9835fc6c470d33c2ae34eb251201981b68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823256fac6b050e4eacf69f3f03327e22", "guid": "bfdfe7dc352907fc980b868725387e98f6e1c4c02ae2d8d757c732fa943a51f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3f1f6fb9dfd8da1cfa233fa383345ad", "guid": "bfdfe7dc352907fc980b868725387e98135af7b34b4657f8be894d224ee09dae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de277545eb9b390051b4ff6a2d81143", "guid": "bfdfe7dc352907fc980b868725387e98db3d7943841305ffa624937b232033d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866283f60421c0ccf47239e0455fc049c", "guid": "bfdfe7dc352907fc980b868725387e98623bd795fdcedcf533bd9a21d0d3ca51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c10a45b8817cddf1cd9bfa785b678e2", "guid": "bfdfe7dc352907fc980b868725387e981e0db4130cfeb84e43bb9917647f0d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ba6af27e9ecff572102de9b4ca498fd", "guid": "bfdfe7dc352907fc980b868725387e98ec4bdb2554ba6054ae224ed145fc805b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a25a181f98e1c1ae05a458686be9c04b", "guid": "bfdfe7dc352907fc980b868725387e98042e6b6d0bc52333f805fc8a3354fc37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980995076d788df7980b8bb9a3c4702b1b", "guid": "bfdfe7dc352907fc980b868725387e987527568fc16121211d9d0f3fd1c15c5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b9f96256e25b4f15ca02041c0b88a8e", "guid": "bfdfe7dc352907fc980b868725387e98432ce9a011cb4d36854c47ce53514b0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b99a9254799b3e29595dce9ff3f6cbb", "guid": "bfdfe7dc352907fc980b868725387e989ce4fc3e65efb089bfb217a3726dd28f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e7eb9958388460fcd3c4d497a7b90a6", "guid": "bfdfe7dc352907fc980b868725387e98563bfccc85e02b1c8366cbc02e6566a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820feafef9f434b0563ab9fb7994153f8", "guid": "bfdfe7dc352907fc980b868725387e98a10f5afa564114a4cd422dcfd81969b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f8094d50cafa4b10b3d6cb0bca97be5", "guid": "bfdfe7dc352907fc980b868725387e98ad647629e9006f9c000efa8684a72934"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98946713b20f557c31d3dbda0916a3c835", "guid": "bfdfe7dc352907fc980b868725387e98629cea3c28881d897b7ccc00c058d142"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987edd71aafb3504291ebacd0451187002", "guid": "bfdfe7dc352907fc980b868725387e9864e6c76740c246c933125720d8981844"}], "guid": "bfdfe7dc352907fc980b868725387e98df13d8a4601552c92d03928c7a73579b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9811561f0a399b045cb4358eecf5f99498", "guid": "bfdfe7dc352907fc980b868725387e982bdc532d20c01d958db7391bd186002c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb6a89334a666198529b3f162b38fabb", "guid": "bfdfe7dc352907fc980b868725387e98141d35049708fb899ffb81a8cba529ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98768f95560f4521a90f03571e968ca6cb", "guid": "bfdfe7dc352907fc980b868725387e98516c34580b5751e64d58729ae4297b43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc14c7c0243ad7931fc342d22912de93", "guid": "bfdfe7dc352907fc980b868725387e98238b34c24877e88caa5b9f2dea24c851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9e56b60ac6d137768418a14a6ab363c", "guid": "bfdfe7dc352907fc980b868725387e98d5fea3d3888ad7674438d84dd8533c59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aec9bfdc16d9335600b801bc1db09596", "guid": "bfdfe7dc352907fc980b868725387e98588083d9a2a730f293eb3996f0d5c26a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad6efadf8d1db16a041c6d7086adcbaf", "guid": "bfdfe7dc352907fc980b868725387e9832b3c50c5ca2499d44a75f98455c026e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fe56de4b3562bf779dea0172779389e", "guid": "bfdfe7dc352907fc980b868725387e98b2a71989845887ba6ccc128a9727cb2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875e919064186cf46b142ac2be33168a6", "guid": "bfdfe7dc352907fc980b868725387e98b34e08b9f71ec186b2ba014fc35d3f0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980289be9298de50b322bfc756af5f4c4e", "guid": "bfdfe7dc352907fc980b868725387e98a9515098dc547a14198ed34263d19997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d45ffce755369e16b1dc16365948552", "guid": "bfdfe7dc352907fc980b868725387e982af02053076c0a2b11ead3ec2d7a5c45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5953575d5ee672fd2c5c26e966d22f4", "guid": "bfdfe7dc352907fc980b868725387e982a9e0537f88e5cb80c1ab24bf21d19ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801936cab8666f5d731dc37d31cec7260", "guid": "bfdfe7dc352907fc980b868725387e982768aeab1dca2589abaccffaba26eb38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98657fd2e8df5cf1ceda5c0255ee2fca16", "guid": "bfdfe7dc352907fc980b868725387e9846c48e03958d5257c1b4cc841be3c200"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de43b388788edfbd6427f2b44a372daa", "guid": "bfdfe7dc352907fc980b868725387e980e8458e6fa9413f60b2a9656fae54cd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e44ec675e1b253fe15b5414c1d408cb9", "guid": "bfdfe7dc352907fc980b868725387e98f6ca4d7a732f77c2790076d6d8b56de0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a47fb75afbf637919f03660e241aa75", "guid": "bfdfe7dc352907fc980b868725387e988beadd88aeab08e4c67b1faf0eebcaa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853d721220a3932415320cb19052bed49", "guid": "bfdfe7dc352907fc980b868725387e98b7cfb7e004d2549700410b5d32305074"}], "guid": "bfdfe7dc352907fc980b868725387e98633c431151f748c83be29c80a8ee4263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98f56c0f9e4be00ba92ab5cc87ac5de9a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147ddd183486f54d4497ffb62c36358a", "guid": "bfdfe7dc352907fc980b868725387e983768531e285c8f5b240b15bddaf6a60d"}], "guid": "bfdfe7dc352907fc980b868725387e989381d5ab3c649778a0ca8c56a3b8df12", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b0cb7690049e8d6d69514a077739f282", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98d62b76e4ece9d0048e63dcbcaad98391", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}