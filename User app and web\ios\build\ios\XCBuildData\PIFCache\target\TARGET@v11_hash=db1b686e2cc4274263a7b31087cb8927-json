{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98048e6a45882d89d80b49680856ce7f01", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_inappwebview_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_inappwebview_ios", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/ResourceBundle-flutter_inappwebview_ios_privacy-flutter_inappwebview_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "flutter_inappwebview_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c2e1c6587d0187063df33c5ec3ea46ab", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872bdb0a538c448a72329fd03743865f5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_inappwebview_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_inappwebview_ios", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/ResourceBundle-flutter_inappwebview_ios_privacy-flutter_inappwebview_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "flutter_inappwebview_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98331ea9dd6089b43067477f1f9fdd6d26", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872bdb0a538c448a72329fd03743865f5", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_inappwebview_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_inappwebview_ios", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/ResourceBundle-flutter_inappwebview_ios_privacy-flutter_inappwebview_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "flutter_inappwebview_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987f761b458e37a2e2c404fbf52b626439", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98add3cde37633d18510269211f0000478", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98eb19da8ba268438d2e6b0b982aba72f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bfdcb212327fc1aa8aa7db13d3eff211", "guid": "bfdfe7dc352907fc980b868725387e98f52bca7935ef546e6989da0a56dcc4e2"}], "guid": "bfdfe7dc352907fc980b868725387e988f724a183ef32c94afce0acaec2623e1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f6265c1f1f4bdacc539aa5c84be2b7a5", "name": "flutter_inappwebview_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}