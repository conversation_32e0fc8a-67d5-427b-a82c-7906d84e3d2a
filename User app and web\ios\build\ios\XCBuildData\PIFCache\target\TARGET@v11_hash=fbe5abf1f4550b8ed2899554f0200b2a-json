{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9816bf501ec85415c960b8ab8fb8eeefa1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a1623887479873546f582aeda3c16a70", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98054642f9c83c96ad93b6f33a0c333513", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c590bbc14e5d3f95ac4c25f98def1f74", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98054642f9c83c96ad93b6f33a0c333513", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987efb7b7807c7fcdaf522af29063248b7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cfdcee3e7e47140dd4bf5259b3176bc8", "guid": "bfdfe7dc352907fc980b868725387e98f0355673d03c6f48aa5122f0f05dd431"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826a52a34067ec593d6b68b1c21aca0e7", "guid": "bfdfe7dc352907fc980b868725387e986ef3b6b6395f58acbf65628131909b09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853431700551c177bb9dda33b57801b90", "guid": "bfdfe7dc352907fc980b868725387e98142ec48033e614ab1f9dddc16e22bddc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0bd3a240718fe450119417cbd84674f", "guid": "bfdfe7dc352907fc980b868725387e98fcee1f19f8be6f5cef14a0686f2898dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986502d557a3bc7767094752a972d0d9de", "guid": "bfdfe7dc352907fc980b868725387e98e0cd3da99a00ba8cce97c56f1c7a14a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ae9be92d6bffc57df25fd4ef137b9ad", "guid": "bfdfe7dc352907fc980b868725387e988699d15dad461f2b624fb0a731f6b83a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d39f34c214cfa4641c6a667a59d779f", "guid": "bfdfe7dc352907fc980b868725387e98e9793ff595e9a0524944ae7cc07d0767"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c927528ef8d151289746ab82b696f4bd", "guid": "bfdfe7dc352907fc980b868725387e985f02c6ba2ddde6b31a0a55f6f75c1a51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc6b78c24349f54db27eae431abba617", "guid": "bfdfe7dc352907fc980b868725387e98d06ffa4bcf66f41f05f358ae01fcf8dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dc610ca0d8989e8d94009a78249038e", "guid": "bfdfe7dc352907fc980b868725387e986f459f05df40d68a478b32b920f28400"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897e23643ce90aa7e48507db1f4828b61", "guid": "bfdfe7dc352907fc980b868725387e98d45d8893a93b99c71a9cd8ff1b963475"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980553b87cf4c37e957df1f66daab350e5", "guid": "bfdfe7dc352907fc980b868725387e984bad19fe2cf7f85342f461a9d7a00460", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98262cc8d1567215fde634817136f214d1", "guid": "bfdfe7dc352907fc980b868725387e98183c81af9da97cf943c733bbea573bbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9821d2ce9cc558624ea0ba1ec3ab0c4", "guid": "bfdfe7dc352907fc980b868725387e98db0e2487e4528b701cb34ff52e2cd2c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dd6eeeb8009904030695fb6f3c2bd42", "guid": "bfdfe7dc352907fc980b868725387e981287a651c1a97b089e9c09758f39d265"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d6fcbf48dc14809d42e376433f6871b", "guid": "bfdfe7dc352907fc980b868725387e98a653e9b09c593492aef6cda7bcb25d21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98240a281a2de8b5e820f089bbb0285b7d", "guid": "bfdfe7dc352907fc980b868725387e98faa881b04b26a8177a6e86595627a789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b333888e050c6b43223c5a3360705f49", "guid": "bfdfe7dc352907fc980b868725387e981504420d72fdefd3ac8ba10607ec9d91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4279cfa147873220c4865a49efa91a5", "guid": "bfdfe7dc352907fc980b868725387e989baa18d05ff9266dac00ba22dfbc8300", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876c347ed6886ccea894999b8de016868", "guid": "bfdfe7dc352907fc980b868725387e985e3af3348295eaf2e99d01cacbdcce2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838c1372553331adb67ee2f6b0765b004", "guid": "bfdfe7dc352907fc980b868725387e987b07af6ca7c3a13ead9d9071cb9a0f4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c302f7cca9c75538b76e9f71b98b49f", "guid": "bfdfe7dc352907fc980b868725387e9837a18ff7b70cd2b89cd9fff855266daf", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987e211ecfebd845853a2253d66a38c476", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986fde68daefd2f18716891d527bb042b6", "guid": "bfdfe7dc352907fc980b868725387e98527bf7c8b5d54b01f2dc4ef962385c53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893195e0c02754be83cd34cfda88cbed2", "guid": "bfdfe7dc352907fc980b868725387e9849d76ee7a74fc2b8042325af38c5abc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a13d9c7d04621053d11cbfc80ee6ac9", "guid": "bfdfe7dc352907fc980b868725387e98a77c06eef80dc17341393db66980fa0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a5104719f06ae7dd0084e68e9aa7743", "guid": "bfdfe7dc352907fc980b868725387e980e29ad3bf1c5cae6a5bb900055fbdd2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d4835a52a84d76f7e1e9dcb803808b", "guid": "bfdfe7dc352907fc980b868725387e983c978b471a5e7140b65f46e773c6459b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df44bc45bfe8680b188fe6f491002119", "guid": "bfdfe7dc352907fc980b868725387e98a4a895d9934137d8f192c264eb067b12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878867b1437cf213e63e2b2d6f698adad", "guid": "bfdfe7dc352907fc980b868725387e98115b90ec1f54d86283cd3e42fed8677b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820fabe07c89704061b991ae05d975f54", "guid": "bfdfe7dc352907fc980b868725387e9873505c56df992a5bcc8af088d65b2cb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b10ca942b64dc66cda4708b3ead4ca0c", "guid": "bfdfe7dc352907fc980b868725387e988e849acb91b6458b15a497ce4c9371ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984af4971a08e90d5b9ba331ebe2036995", "guid": "bfdfe7dc352907fc980b868725387e982b073f73cfadf639a28f5a4448e9a227"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817ea0f5e399a71aa5ea7196b947133a0", "guid": "bfdfe7dc352907fc980b868725387e9888518847db757dcb9a3547b793d8c82c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811f08cfed056300db865e72a98d378fc", "guid": "bfdfe7dc352907fc980b868725387e983a19d44fba0849797f21b2bcdabb5c11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876f780c89a8e147e5cee14d0580ea0ee", "guid": "bfdfe7dc352907fc980b868725387e9812a80385ba4065c146f8eb7e1de765de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd7546401f7ace09a9627bba32761451", "guid": "bfdfe7dc352907fc980b868725387e98e4537d3f57da84e6c545cf9552a1602c"}], "guid": "bfdfe7dc352907fc980b868725387e9868e39632ff027ffc4ff716b259d0fbad", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e987bbaea5474175078fe00b3311f273b06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a786142c4de84e40fbaca86aaf3a5af", "guid": "bfdfe7dc352907fc980b868725387e98e9b1122fbb23b5972571f31b67eccffd"}], "guid": "bfdfe7dc352907fc980b868725387e98706ef85ac1fa0749320f476a5eea48b0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9809a3bbbfc1c7840f65737f947b5de329", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98a7f406f292a5b04d933aa173304a688b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}