{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9886dfbf8c028df8dff8ea2a2312d21920", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ab9759c01c118828e6a7afe450bffd5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862073a6cd66047929452970fed7e6c78", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed278e5f520c525a2067932639ada823", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862073a6cd66047929452970fed7e6c78", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ec852a8d1adada10d1ac783212da0ea", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98667c601cc5c2f5e862315fcccc40e070", "guid": "bfdfe7dc352907fc980b868725387e98886cce8d89eedd0e8bab1ebae394ce44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1ad1d57e6318121d3593756ae83d88c", "guid": "bfdfe7dc352907fc980b868725387e9814d4634efa63a6d2d505baa31e1ac713"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829e5b7be0ef9b7b1286665ca75edde2e", "guid": "bfdfe7dc352907fc980b868725387e98320cd36a8701febd579756d6afaaf887"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a709f002e7bee87a4a2237e1bf94745", "guid": "bfdfe7dc352907fc980b868725387e989fc70d6b14cc9ff6f9284594eda21c4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898985f096b8295dc12686ed4db5921fb", "guid": "bfdfe7dc352907fc980b868725387e987ad8ad9016d3b0417c705cf13bf56cd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf01a16bd305509696f4ee9dff007108", "guid": "bfdfe7dc352907fc980b868725387e98d703dfd4c027fb2c6536f0b761e5693b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1e709a7bcdcd180eea90a066dd0f1e", "guid": "bfdfe7dc352907fc980b868725387e98e3a5d36fe9ddc383dd52cfb2f4aa3d2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989edb1808b733b304bf1b7304f84a8dbb", "guid": "bfdfe7dc352907fc980b868725387e988c0cbdb0ce4d520f771f0a0119fc0a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826286ffed00e4967bb0b11c04eb317c6", "guid": "bfdfe7dc352907fc980b868725387e9841f34774f1d1f35fd1421e074705026a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982001c5701b443ff95dec87d85187c3c9", "guid": "bfdfe7dc352907fc980b868725387e980709a52f0b40d2f4f632a567e77f0052"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cadb4fb5a477741108e3fa753b1810c1", "guid": "bfdfe7dc352907fc980b868725387e98e7e5ef36f766a85235666117b7e3af60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98833a001e8831e830a799ac9b0033d190", "guid": "bfdfe7dc352907fc980b868725387e981219ec7c40082ae325734559e6ff1972", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e9b9f28c8514676ffc5986c49c87e2b", "guid": "bfdfe7dc352907fc980b868725387e988f15e339179ba61cee0efcba36dc94d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983694f7807011f91d3cf878124f1f0e4f", "guid": "bfdfe7dc352907fc980b868725387e98c613f6e9f1268a6fd9760f88f3ba1cbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d3c8c5d12d89e4cbee1743975a66623", "guid": "bfdfe7dc352907fc980b868725387e98cef3f95e150d8d212f3fd2bc94dbbc73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98111bcb292662c710193f96f47a744d31", "guid": "bfdfe7dc352907fc980b868725387e98c03b9eab66a77826f26ccfe61cd18596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a92dc9959f7508b43e5607d7ec8650", "guid": "bfdfe7dc352907fc980b868725387e98005c9eec2b5c8702ffcaf744ee071c9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876660348a8ed7ed7f6da5a497e5c026d", "guid": "bfdfe7dc352907fc980b868725387e98bb76d4700128219548c4ec6ddd809bdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862982356ce4aeeb3e7c65e37cdb9870e", "guid": "bfdfe7dc352907fc980b868725387e986927c359f2dda2d0de174559b9012d84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818a134f2d58f454282380dd3acbcfb2", "guid": "bfdfe7dc352907fc980b868725387e981e07ec96bdb3b4f1c8571c17df42f556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fe774fb79c47f6848e8a45b371b6aa2", "guid": "bfdfe7dc352907fc980b868725387e98e8a59315c0e4f60820a2913515601152", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb64d5db62b8e5c97d2c4dfa7ac223ec", "guid": "bfdfe7dc352907fc980b868725387e989370a0471632ea106ad7ef12faf7eb58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ae524fcbb41194ac3da5be884992be", "guid": "bfdfe7dc352907fc980b868725387e985a8665f65a5b1496a289fc9a37fd5d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f429755fbc3c7d351beba2e049aed58d", "guid": "bfdfe7dc352907fc980b868725387e985ffe850fe17544bc1ca5e12098182a72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984568d08da043c971e3fbb5fedc2e782f", "guid": "bfdfe7dc352907fc980b868725387e98103c9274d36cf279ca06210c7d6fc9d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd465778e8741dcafb189970a3e54d1", "guid": "bfdfe7dc352907fc980b868725387e9849661bc209a55c852f76889cf6a1e997", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f8839f78fd084d1b7ca90c1e6bbb9b", "guid": "bfdfe7dc352907fc980b868725387e980cec98cb2e90c63aa654a612ef32167e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a76032bc42d55fb82df04626a87ef23", "guid": "bfdfe7dc352907fc980b868725387e984db1948fead7015391334ac8735be24a"}], "guid": "bfdfe7dc352907fc980b868725387e987f5b8c6929a27c0c9bd2e06b911bd5b4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bb7092a38ff582649c383bf0e41e3b93", "guid": "bfdfe7dc352907fc980b868725387e989fd85f031f44e7e867837755a95d792e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a689209d78ac64c0f31f4f4bc16e2f", "guid": "bfdfe7dc352907fc980b868725387e983a357ebf5ac631d9351567fa4c1a590a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df4322ee22d429158253d194fa0c5763", "guid": "bfdfe7dc352907fc980b868725387e983ff901e1c348f871af84eaa18946dc86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be8745540d577e1b77fbc9537c5060c6", "guid": "bfdfe7dc352907fc980b868725387e986e0959b19781b54bbcbee026cba50b57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98297c1fb9be7cfd3775f6cf794f97ed2c", "guid": "bfdfe7dc352907fc980b868725387e9840c005f94a80219ea5597c3482850da9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98103134c0a547f4eb7659ef27c14bbcad", "guid": "bfdfe7dc352907fc980b868725387e98425fb0d289c9745ca8ac6723d38ade98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bee35d1a8f28e50b7036a1a56307b038", "guid": "bfdfe7dc352907fc980b868725387e9847bfe48de35e2a18da782a45e7538de1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98374a6aaa901916db0e340d1edbc317f3", "guid": "bfdfe7dc352907fc980b868725387e9801fe482d894d5861f713698ed9ee48fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1cb2c3124544107da3f8ca210c7d7e0", "guid": "bfdfe7dc352907fc980b868725387e989761f53b0f553eb48fefe5e27e6df52a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfb89ad02f30a49604917b7c4c7ab5e9", "guid": "bfdfe7dc352907fc980b868725387e985e004db72a88aabda259dd96fe2d878e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98315eaa3a8d33b0d49349a07113a0222e", "guid": "bfdfe7dc352907fc980b868725387e987403a31bf817ee8bcfa02bd720133be0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989df14601ae5166a3b2748ef5b0d26d5d", "guid": "bfdfe7dc352907fc980b868725387e983e4e4a667d359ad7936ffb1a019c396e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec342b535f7f6886ad9562e4ea0f5db", "guid": "bfdfe7dc352907fc980b868725387e98dc10bd3e500f94a88b8237eb3d5976eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850daa22676a96158a957cffeac24f723", "guid": "bfdfe7dc352907fc980b868725387e98d0d3e46f3d082a79fde397aaae6ff8cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df3a42ff8ac648082911707fb0a847da", "guid": "bfdfe7dc352907fc980b868725387e98915f338ff896f522a21633c22b745224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bea418184d2005f7abf217e00fc55261", "guid": "bfdfe7dc352907fc980b868725387e98f6e22be9228fb66854e2d978db3d0fb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efba2b9af5b02da92f627878787121eb", "guid": "bfdfe7dc352907fc980b868725387e98ae74b6a1ad2caa4d0bed9d8659c899db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ed1e0ae559a52c0d37c948efc709086", "guid": "bfdfe7dc352907fc980b868725387e9847a9e3ab0f288ebfbc84fcc9dd22948b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856d990ede745de2d01124e4518aee31c", "guid": "bfdfe7dc352907fc980b868725387e983247400d15e10e10db86e4c31c9710e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98538a970c9461e7866fa95c197c3446be", "guid": "bfdfe7dc352907fc980b868725387e98b0e3693309aa6dd9fddf8e4e0c1ef477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98483ff0c160acc676ee79e88282d47594", "guid": "bfdfe7dc352907fc980b868725387e98b58454db49475740727c589fbd08bd53"}], "guid": "bfdfe7dc352907fc980b868725387e984f5e94e465f5903eadcd32566152e573", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9888b6c3c65f5991bfcda51a9f62d2b166", "guid": "bfdfe7dc352907fc980b868725387e98d5751d5d5edfe95cbeeb7320d75636f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e862e3eb208a928b09c5f864ccd790c", "guid": "bfdfe7dc352907fc980b868725387e98606f654508a766c82daac06f2cf31545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e989ba13964eec132954b26eb3135d7fe0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986912a28286737fa1318c8c4b807ccb76", "guid": "bfdfe7dc352907fc980b868725387e98a86e502fca8e7bbfc825b02ec2da6001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147ddd183486f54d4497ffb62c36358a", "guid": "bfdfe7dc352907fc980b868725387e984b2fdd52b6577de327fc05ea8cc3e6eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981925823dade64e954318992e61d3ff24", "guid": "bfdfe7dc352907fc980b868725387e9829688bd5d8608456cc667396d5869b23"}], "guid": "bfdfe7dc352907fc980b868725387e98b2fa6105a1ff6ac5133924a8b9d0316f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98edbfd520c3bab10d90ee1126f6a977b9", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e987f0db34de032e064343027920ce15adb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}