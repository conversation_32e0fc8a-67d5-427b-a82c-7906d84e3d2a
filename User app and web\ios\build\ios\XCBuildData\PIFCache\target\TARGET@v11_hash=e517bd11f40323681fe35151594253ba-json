{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce9b4dae99b487fd826e9a73a2d1729b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989e72b2394f3cb11b786f8bd50770adaa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989e72b2394f3cb11b786f8bd50770adaa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988ad22395af178fbb62e5ced0193a8d22", "guid": "bfdfe7dc352907fc980b868725387e982ef6da7a3a60632d8f8022694bd66dd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983644e0b43bedac767cf4871633ae4c3c", "guid": "bfdfe7dc352907fc980b868725387e98d5b97975f2350a08cf53ffeffcac67ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e242f01b0f1db42356968b554813d39", "guid": "bfdfe7dc352907fc980b868725387e982a831a1daaca5b26d69021efadf726c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8990655492a21eefc6d3b7f82011704", "guid": "bfdfe7dc352907fc980b868725387e9893021a663ec0153aef3852a58d009c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983eabf3cb391f0bed71b2f89284698826", "guid": "bfdfe7dc352907fc980b868725387e986796d36a2bdd3edb6b59e7129fd5a2bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5c42736e24885d2e032780ff9fd070", "guid": "bfdfe7dc352907fc980b868725387e982e3cc8a1179bcd8b86b68167afee1e90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dd2513ede4352306e656c1c032dfc99", "guid": "bfdfe7dc352907fc980b868725387e98c0be046fcf8cb428c9d30ae21d942726", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c33498d58ae6f66d6bf1f0078a58e3", "guid": "bfdfe7dc352907fc980b868725387e9823853b970b232b2ee98e8e4fc95cac83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5e1bb2819422cdcea9cd4998d974b28", "guid": "bfdfe7dc352907fc980b868725387e98f79b7fe35423f642e91efea7931e4532", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b71f6c854bf6e04592ddc7ccdfa4fd71", "guid": "bfdfe7dc352907fc980b868725387e984f6b3b7d4f466465da9e74bca70c619a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896e074babb780f4bbd5d0e1ae9fa09e8", "guid": "bfdfe7dc352907fc980b868725387e98a21439c050c6574e9eda2a7c51d789c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506fbd1d0940d8cfe3ba266929ca49de", "guid": "bfdfe7dc352907fc980b868725387e987ef5d73bb1bcb9d251b3abc88056c8c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4576bea44de1d6f0a4eebcdde32e929", "guid": "bfdfe7dc352907fc980b868725387e9834bceb04a1a4262410f8691a86e50ac0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856677c67392186b21d69bf9cf8a55a3a", "guid": "bfdfe7dc352907fc980b868725387e9819a72032caea2c8a2a1d4d8a68690ede", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879e44423e02c1e1a0d5cab6e9608ecfe", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98747e92d9dda1564a7a25e04dd2c72df2", "guid": "bfdfe7dc352907fc980b868725387e98446126075935aea39a9e1063fe80734a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2eb646804bdfc3483c96c7260664eb1", "guid": "bfdfe7dc352907fc980b868725387e9852b28a641c87eaad7787397417bf81c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bb6fb99aeb838a2df85e2c3c992aaba", "guid": "bfdfe7dc352907fc980b868725387e9850004e812d7475a48e09f9d39d1fcd0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d2df2e061940cf8ac9bcf53b9141ec8", "guid": "bfdfe7dc352907fc980b868725387e9849968048093435d2a565774782e9f29d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce52c7814e90cb89c8d19293f8c76582", "guid": "bfdfe7dc352907fc980b868725387e981550500afbebd6a16545417108df5269"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859de36b2695f9c3c8a7fea47afc5e718", "guid": "bfdfe7dc352907fc980b868725387e989a62207c5988db84ddcac0078af53d6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a443c6c9b482f9f2d3056648eb5cc434", "guid": "bfdfe7dc352907fc980b868725387e98d0e36eae7c250ccd059d9dcc1ce416eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f1c595591439ddb5e786b5af56b7526", "guid": "bfdfe7dc352907fc980b868725387e98aaf08e1539082f8b542f01b83c7e8d18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d8d9c30873d11043ff93b4c8329cc29", "guid": "bfdfe7dc352907fc980b868725387e98c04b1033e4e3e2b45fd298d396b5d875"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890956fcfa869bffe8fb620ff63721f87", "guid": "bfdfe7dc352907fc980b868725387e98b2e8dea6cd344be14bd1567b9270e89b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e683dc26f7918931bb64dfeb0cfba7", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}