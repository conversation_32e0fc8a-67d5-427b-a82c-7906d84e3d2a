{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0d47264a92705749f616fb0deea9041", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98582ee12191c9e7a5bc81fc36cdba5015", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847110eb0f8c79069dd9d984432d22d82", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9817e57f3b3bc56858a62898c48bd16e02", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847110eb0f8c79069dd9d984432d22d82", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9812fce2b5a760adb0c58cac8dd3624e75", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f11e4827a392d30b4f600aa37cdd58eb", "guid": "bfdfe7dc352907fc980b868725387e988f38ad168784dd87791030e3926d3d50", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9823d3bc07ddddf8adc42d46227f1fadf2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b4e2a17d039dd9a8277a1df42776f14d", "guid": "bfdfe7dc352907fc980b868725387e9888e4441df4ca036804e84cae5ad51f15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6ba6ccf07fc52140675a2881d3f3305", "guid": "bfdfe7dc352907fc980b868725387e98a6c429611454cf64e242fb2872f2a7de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816fe44369d1b3ffbb7502abf3dc9a30e", "guid": "bfdfe7dc352907fc980b868725387e985513114405b593d14e934bc9bd54bbd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985716cfd655384a2386cf0168d54b276a", "guid": "bfdfe7dc352907fc980b868725387e987134823622b5e996d2d2818cebd941d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829db5b8847802548289884bb56c81870", "guid": "bfdfe7dc352907fc980b868725387e983ef04378ae164bb6410fc3e8aa90d81d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808c5842c4f6ec9e5df82fd0bc7cf6031", "guid": "bfdfe7dc352907fc980b868725387e98baaa53257e48d3698adff508bea2b068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5a5478f8602021940cf83b6522eb2c9", "guid": "bfdfe7dc352907fc980b868725387e98bbaecc191595c711bcd788967b4e4546"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecd858dd916d8266189b92c76c533c90", "guid": "bfdfe7dc352907fc980b868725387e98e16cf084eb539299d7526d51d690e3b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3c4ede751be4878e7c6f02e57c469ee", "guid": "bfdfe7dc352907fc980b868725387e988c98ef034cc3805057eb3d48129ee62e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b047552c8d7f200dcb1a7e3a5cd4cc8d", "guid": "bfdfe7dc352907fc980b868725387e98cb25fbf0ab52afbfdbbd6278d94b320a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da47fa623b5284b25231ba10fd82eb9e", "guid": "bfdfe7dc352907fc980b868725387e98fe78920f228a4b5c3e635f30520bfd08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d285057882a64e9297b83763a9048b0", "guid": "bfdfe7dc352907fc980b868725387e985554b94cd61c178e777187e4937eee90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888177ab3e3a4385167578de080d8e0b1", "guid": "bfdfe7dc352907fc980b868725387e98a73a951067d1218905769135c2c0aeb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802a29a5faf41011385f0823968a20e4b", "guid": "bfdfe7dc352907fc980b868725387e9870ac26cc77b273c66e5c1fa90c577bb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfe64618f0982f652e2b4210be5f7416", "guid": "bfdfe7dc352907fc980b868725387e984494165c408ec807186749edb969ee55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d6b214a3423bc1156e37c0bc29cc84d", "guid": "bfdfe7dc352907fc980b868725387e98c74c66884338d862daefdadc8e2663e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3817156bc4d4e3bcda0d07861be8354", "guid": "bfdfe7dc352907fc980b868725387e980f78f0fc8094bff1a5f0a069a89565bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d446df3564055f4faaa5a1c63646c769", "guid": "bfdfe7dc352907fc980b868725387e984b48adfb42e1723254c346953898c085"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b1721cc7da9a8ed04e58b39056a88c4", "guid": "bfdfe7dc352907fc980b868725387e983e23bccc132e276dd3dec68d8f36d853"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983590f3a1d2eb74e41f948d79e3668926", "guid": "bfdfe7dc352907fc980b868725387e986f4b25cf006d711e389cb139a9e0036b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0e4925129512c358019c6398831155", "guid": "bfdfe7dc352907fc980b868725387e98e1d6e6c94bba422489858ab943dcc455"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be44b2b0d134d04df4c4ef12a753c678", "guid": "bfdfe7dc352907fc980b868725387e98ebee2007fc5627f21b2a5344191659a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3b7be6ae31d42eac418dda8fd7eca14", "guid": "bfdfe7dc352907fc980b868725387e987fa6951be071984b756abd73d93bd407"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e57c8cd2b30efd9382d5eacc638937d0", "guid": "bfdfe7dc352907fc980b868725387e983c5ea0729adbe634ebc1c8fb7920eecb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98388c6f81fa2868cc4be68c77acd66fe3", "guid": "bfdfe7dc352907fc980b868725387e989e2c46ce841472c29a45c789220b4e08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aa55bbfe57417b987853eedf4011049", "guid": "bfdfe7dc352907fc980b868725387e9828b7ac0192486c30223333420118f4b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983216d90fdc87bee3cd66db0a78f0268a", "guid": "bfdfe7dc352907fc980b868725387e988c191c7cb91eb7ebb885dfc4131d1f57"}], "guid": "bfdfe7dc352907fc980b868725387e98007cb0079a7779d4ac4f97cf4dd23f1b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98972dbbb3741eca0374653324015c571d", "guid": "bfdfe7dc352907fc980b868725387e98ea4a4be18380110b901370df43591c64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98614ad39d4a033dc6053f8e9e542ffd47", "guid": "bfdfe7dc352907fc980b868725387e989318850bd940ca6bba3d5fe909a14fed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e982cd9be5f23e0aca19b63810a0916990a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a50bb069c5d58b85858f791831de2c78", "guid": "bfdfe7dc352907fc980b868725387e98bcededa1bc15937a8a9e2fffd06ba93d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a786142c4de84e40fbaca86aaf3a5af", "guid": "bfdfe7dc352907fc980b868725387e989ba8c63d5fd839d11ed2c9e5a70fa2a3"}], "guid": "bfdfe7dc352907fc980b868725387e98626875825b8bb4445cadf31ed6161efb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986fc1b8b1f17d7fa1b49771bf622284f8", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98419d840442bd00e32336e2f5b41f10bd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}