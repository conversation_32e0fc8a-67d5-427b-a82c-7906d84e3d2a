{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984afc60a90cd6b48fdf32335e5a98ad1a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984b0802f1fa11af57830cbeaecf82c9fa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98594bd3ebe8a292f330bb12039cf43eb6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9885ed3f1d3a462eef54848f47d879f2ac", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98594bd3ebe8a292f330bb12039cf43eb6", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9846e127ffd671256a1f22070f5b6c3e39", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9851d5967223e7939c387ffb791c5f7253", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c7c7f106982f1b794d7920a640077ce1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b697043b41e5e392ac425cfd4dd952c3", "guid": "bfdfe7dc352907fc980b868725387e983cade08eabff227333b9e15cc096dfa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983000b77b42733504afa8ebfa963d7bab", "guid": "bfdfe7dc352907fc980b868725387e98bb5caca165f149564ca187c9357ff01e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98933fa90b8e660cd52ced8a5b812025f8", "guid": "bfdfe7dc352907fc980b868725387e98ffb74e8ac043c885a978f2ee9f12ebab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345f19dd792ff874ac094297ea285545", "guid": "bfdfe7dc352907fc980b868725387e98498b959aaa34f5a658db5497a1825b21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4d789082056a9437cee541d1eeda896", "guid": "bfdfe7dc352907fc980b868725387e98b1ac3a6b5cfc15b613b3e137a057f1fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dda1b474b8a3f01cde7c80239903b23", "guid": "bfdfe7dc352907fc980b868725387e984357c004df8bfee528efb6788aafa57b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb2ff484c27d74468392412d8b9d0f25", "guid": "bfdfe7dc352907fc980b868725387e98c69e3ace8c6871d0ec2ab6f5141886c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ce69761cf2b2ba850425c80ad7cb66e", "guid": "bfdfe7dc352907fc980b868725387e98f669bd0cb0fd9d318912af9a5b2fc9a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb36adb647c558e7e28e66bb054c1e4f", "guid": "bfdfe7dc352907fc980b868725387e98e9bacdbf31407d0e5d3ec7389952c560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981750c29afee461f48408a4c6344efe9b", "guid": "bfdfe7dc352907fc980b868725387e983535200ddc9fe751bf2c16662b8796c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aacd9288fdf67f8b3ab35959a73c7873", "guid": "bfdfe7dc352907fc980b868725387e989a62e3cf1203395d2eaee0c57a19512c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981840622c3b1c44fb2fbfac3b22ed4427", "guid": "bfdfe7dc352907fc980b868725387e9801e8c2569ec57c4afff9dcae9a45ef9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1d1ca53eb4fa79abb54c68589e6be6b", "guid": "bfdfe7dc352907fc980b868725387e98c3121fff48d811efac76fa96a7333008"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b730e04af28f5c1c5a39f64176568fa7", "guid": "bfdfe7dc352907fc980b868725387e980869c75da9d023d2c2b6f3a51ed73cba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d349b97a1642abfe4606e4f78645785", "guid": "bfdfe7dc352907fc980b868725387e98bd9b55ef3aa5b53bff1cf4704100df0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb5593bafc60f595463c13ac18fb65dc", "guid": "bfdfe7dc352907fc980b868725387e98979bd0ccf04c1f7b73ff65bfe2049e41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a74a6988bcb4164c67b3a40b1b5f3c", "guid": "bfdfe7dc352907fc980b868725387e981a9911e4d3182d0e81cf69918c2ad787"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986345c74da6b4cb10a21cfeebf0589e91", "guid": "bfdfe7dc352907fc980b868725387e989f331e2aaf0e351759088b90b74b8f8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c5664e0560c11f1c788883df68b0297", "guid": "bfdfe7dc352907fc980b868725387e98c92496f3d95982c1c9b9d94e69c93962"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98400c8e5b78a1edd0a3b0ea8e247d7001", "guid": "bfdfe7dc352907fc980b868725387e98421ebbe9bf3143f6f696b7dcfa412df4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e2a02d0bfde9a41490f64c1dad997fb", "guid": "bfdfe7dc352907fc980b868725387e988d27e4cd5406fc58d302d2977f47f3e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985abe75eb7d9b6b01aaed26b75a6f565d", "guid": "bfdfe7dc352907fc980b868725387e987a9909b5c523b1a72792644b32f4a9e5"}], "guid": "bfdfe7dc352907fc980b868725387e9807551dd397eaa15e1634692487ca4204", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}