{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2176fd9359b634b6e7e79ae4d1e2998", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2067621fba6e571a9a21273256f25a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d2067621fba6e571a9a21273256f25a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98870f89c6be233272a6a6158ed9da4ffe", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e0ec08dc0e3ecc8752915d27cc9ec3d", "guid": "bfdfe7dc352907fc980b868725387e98830df83e0ec997753ec519538ef6f60c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984db1de1b5bdd37e07e1d2c5897e0aa39", "guid": "bfdfe7dc352907fc980b868725387e98ccdfea99e0cc660b9001c8a5d5adb33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895bc1b2e0bf2cf3d80ace8ce916c16d2", "guid": "bfdfe7dc352907fc980b868725387e98dc5de76e264055f43c861d8493e01121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d0b03953555c03616f7ed3a54e732b6", "guid": "bfdfe7dc352907fc980b868725387e9871791bf8d2f68b9f58e2fe1d470f4886"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a4def75bf428309c551dbec53d1d6f", "guid": "bfdfe7dc352907fc980b868725387e98250472815ebabfbe2170b8bbf902db8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853c49d2cf9aa93d95e98e2b544056fd8", "guid": "bfdfe7dc352907fc980b868725387e9875bed1f957181399dbba774b75f2fadd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba7fab70f5ad22a309d705e3a716d092", "guid": "bfdfe7dc352907fc980b868725387e9883888c5b2799e1a0437df6bc3e5fbba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3db75c5e293e0b53e5065c1707c72fc", "guid": "bfdfe7dc352907fc980b868725387e98e6c0000aa9967ac168d5c88601b76b16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853e975310bc345d4c7b5e2ca31531304", "guid": "bfdfe7dc352907fc980b868725387e9808eca2f1587502e4fb0253dd70590083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819001e067ae012a0a74b8162ce0c128c", "guid": "bfdfe7dc352907fc980b868725387e98a72d821a485fdcedd26adedfb0a2b23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28c82bffb43c9741d770f25844cab81", "guid": "bfdfe7dc352907fc980b868725387e98ca4096acac43a317bda5ba0c47bb8bda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98549c65cf79ab022dc28942d24edce301", "guid": "bfdfe7dc352907fc980b868725387e982ad692615143ac287df3354fda01c162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985003909e28359f0f27a29f8346c41624", "guid": "bfdfe7dc352907fc980b868725387e9864b3dbf6d6ced697454ea174127f7c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6545ddd3e55577fef30c9854885e32b", "guid": "bfdfe7dc352907fc980b868725387e986fa224351bf07d506906474ad5f24fc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e37e2e0646155d94cbdb8b2dd2fbd5", "guid": "bfdfe7dc352907fc980b868725387e980c4b58a8297e745a2a277b5c4d9247fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8a1e1a988758e285f33b597e3996644", "guid": "bfdfe7dc352907fc980b868725387e9812491126bbc5062ff02880e7b428066a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f92305ade761e58323d218d69a115e7", "guid": "bfdfe7dc352907fc980b868725387e987c7759cedb074971adacad0051eaa2c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d694dcb8d737cd405eba2e2b987be37a", "guid": "bfdfe7dc352907fc980b868725387e98ded215c4320567521f37c44a3f5f8d43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e803159b303fb4d80056901c036dce", "guid": "bfdfe7dc352907fc980b868725387e98d1f5a1d290b761132be798590f0e63cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2a4cca136ed573ec4c42fd66a11c72", "guid": "bfdfe7dc352907fc980b868725387e982fca89ef90efb3c5d03b926ccf48972b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982473a93ef0b4630d03f27f2d7f886360", "guid": "bfdfe7dc352907fc980b868725387e98724a5382285ba30611c6c80c7223a6cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987281b9acafbb4b02fe339d99c4af4792", "guid": "bfdfe7dc352907fc980b868725387e98314e1d0592601eec0305df273487779b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae68f9691527051f7dd2dca0f0fb31ff", "guid": "bfdfe7dc352907fc980b868725387e982da34c67b6738b27c0b8164aa29b2f6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1614895253f10db937e5af119d2aeed", "guid": "bfdfe7dc352907fc980b868725387e983f12e6082182025f7e041ef080820aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98378feb41334aac218c3e87d201a231f4", "guid": "bfdfe7dc352907fc980b868725387e98cbe2590344c31ed3a21c3773426e3fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844ea3e425c3060533fbe61a8fee23aaa", "guid": "bfdfe7dc352907fc980b868725387e98b35df1c3f81bed4424b8b446948a1c70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a349fe2719756956262b7c8e68c1668", "guid": "bfdfe7dc352907fc980b868725387e989eb0d23d507b408da8575a7e972a2cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800b4f6dc9b8266476aaef66eb41cde61", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893161dd76b778fea7df7752d3692d8ec", "guid": "bfdfe7dc352907fc980b868725387e98462265d7e73c937aba0c1a0c7a05f7e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862f1e099ac0154927f81b3e1716ae5a4", "guid": "bfdfe7dc352907fc980b868725387e989cc343ddd199dcedcf3100c93f46534f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98621c51a4eb05506f50c65f6ca86ec36e", "guid": "bfdfe7dc352907fc980b868725387e98ab5fcee13c8c4444d60bddb6a295be5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987640fcc8e70b4269225dfdf29a5a4ac0", "guid": "bfdfe7dc352907fc980b868725387e981f3185aeafddcb4f6ad2369d0f8b8b8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e8c8b06102edd8997aaaa33cca3044b", "guid": "bfdfe7dc352907fc980b868725387e988129f147513ddb8d2cd36defbe96ac9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af975446300bed02de74f5ae210d1155", "guid": "bfdfe7dc352907fc980b868725387e98a160350763ead235ae8e93e475392e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890b7d708d5e0d5bca3927a2142208daf", "guid": "bfdfe7dc352907fc980b868725387e98164e73f85bbfb94e6fd4ff8e92faf005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c396a6b296b5d0bd0f3f786ad5e79a6a", "guid": "bfdfe7dc352907fc980b868725387e982ca5c8128c4301080ce2ab76a87de2a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f3230f4d4f656f4c2c304628baed61a", "guid": "bfdfe7dc352907fc980b868725387e98a3aa5c4326493008dfe5627384906f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983939b2aa37da758aea4a7602cd2cf99f", "guid": "bfdfe7dc352907fc980b868725387e983ef3a1fa9beea16913e59206e92a27fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983eac773aee28fdf63882fca6e903c32c", "guid": "bfdfe7dc352907fc980b868725387e9894d4fc4cdf98039d9ddf9524d12d795d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f57a4e9556a3e778021ab34b3fde5d45", "guid": "bfdfe7dc352907fc980b868725387e98db847b3345cc1944e6e7659ddef1e272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d38c461284d05cef5b252d6e8b1eed", "guid": "bfdfe7dc352907fc980b868725387e98ce1c4d48c89aab4d580616508036dd9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2813724d81aa0e7a58f88c8685e97a4", "guid": "bfdfe7dc352907fc980b868725387e9819971fed787079059c7146de03897424"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd79c8fc74ae011a811df93935952764", "guid": "bfdfe7dc352907fc980b868725387e985694432eba2505c3088b40c4577e6121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cdaf00b7667ebdcec73bc3fc045dd1f", "guid": "bfdfe7dc352907fc980b868725387e98e100eee6ab91604739cd83bc225305cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98348b9b3c4740e13adfc67b52a4096cfd", "guid": "bfdfe7dc352907fc980b868725387e98f05e2e6f5f20f2097468207b2b08f057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d318179f59b29b31f8f6cfd24957760", "guid": "bfdfe7dc352907fc980b868725387e9829d94119edcc321aea742c339acb7459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985575dc8f782e7f485133edc92b9911a7", "guid": "bfdfe7dc352907fc980b868725387e980facc7b14738d485db14bdf99debf547"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e7a23a3f9724785073edd537b16a6d4", "guid": "bfdfe7dc352907fc980b868725387e9875aab928f91efc50bca0b3a4b53323ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d07fcac8d381044ab9effb92a24bcb5", "guid": "bfdfe7dc352907fc980b868725387e98cdd19bdba7752c5cd9ec9adbc95dfd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856465c167c50c2772fd681654f7cdb47", "guid": "bfdfe7dc352907fc980b868725387e98e5de14b5482be462d4bb44c08a0b4e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acf3193c5877e7edcb5a3db2de8e4d23", "guid": "bfdfe7dc352907fc980b868725387e983cea68a78c71ced38e5925a70d2e4d0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e3405b960b95110a3531d8173e42e5f", "guid": "bfdfe7dc352907fc980b868725387e98811167c16fb4f59c13d4f45c4b0bf823"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810898c58933c6cb69b494f301db21a1e", "guid": "bfdfe7dc352907fc980b868725387e982fae6087faf90913c54ca9ba872a78b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827063086ba6c5a45d5a3f2193f05d8df", "guid": "bfdfe7dc352907fc980b868725387e981dda166fea1d00755baa7c2e29f91792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981693dadffb5bf882e02c3d33f39ed509", "guid": "bfdfe7dc352907fc980b868725387e98c27886c8f0e54a0a2e68beffe517c4c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f93e1926d88f7e4accb9e9d5fc407644", "guid": "bfdfe7dc352907fc980b868725387e989da49c25cceb0f746dc7b6d52a264248"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880ed1c7f672328a5f1fee0fdcd16d46c", "guid": "bfdfe7dc352907fc980b868725387e98c2daa5cb5599c651b6c709acea822f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f605b7dfbd5bfe1119f3edfc8c4633d", "guid": "bfdfe7dc352907fc980b868725387e98f995a9f501a217d614b8f4b2cecf3390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a3f3a3a774ba8c47ee851f30674099", "guid": "bfdfe7dc352907fc980b868725387e98d2ff23ba143638be7746ca5b910f7369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae20eaeee833eae4b3c67f14c79cf657", "guid": "bfdfe7dc352907fc980b868725387e98bbffa023bc2ad159c246eb78792c3849"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aa0224b563d23331fac8836436de2b4", "guid": "bfdfe7dc352907fc980b868725387e98aa8696a741ccd4b6741719a926fea194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e7901f9a772f3d257e9df3123c3e1f", "guid": "bfdfe7dc352907fc980b868725387e980b0a11f669d8564f8815d6d3a179b046"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800c52ba4a3c14d8514804e30f361b811", "guid": "bfdfe7dc352907fc980b868725387e98ce47c641f0458341bf50d7b25f1c6a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867e1962fa582b156fdd6ddce7ce3b34e", "guid": "bfdfe7dc352907fc980b868725387e98f9e52fc7467b0174f0e055e0eba47775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3972a105a15c1efefa135566985fcfe", "guid": "bfdfe7dc352907fc980b868725387e98d2b0721788df1c5d87ce43d2ad84dff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bfeaaabb30365df8a5604c97ac967c0", "guid": "bfdfe7dc352907fc980b868725387e98a22b91bc30195e74d25116f730383a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98320035d69722f3f31aff730ac922b4e2", "guid": "bfdfe7dc352907fc980b868725387e98cf64948e2e73b70435172d32ba723704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e38dda1bc2213797f846f26b04b630c8", "guid": "bfdfe7dc352907fc980b868725387e981ad231c682034a6053a672fe7e0b2c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860bebba368b353e96f7fe5c03d4f94f2", "guid": "bfdfe7dc352907fc980b868725387e989ccdfd00db0fd04284ddf335fafa86f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986819751db2a40047df217881bb91b7e4", "guid": "bfdfe7dc352907fc980b868725387e986aa0ed2968449bb085643a863f0937ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868433e1ca45d1b4a0a63c3839f48de9b", "guid": "bfdfe7dc352907fc980b868725387e98e588ceaedef830278f4fcdfa03d515c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894a49d5614a512021115f116f901d5ea", "guid": "bfdfe7dc352907fc980b868725387e98fc667a1b02350a9be46c6509f23aef81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803fd4ed4c8a1ef465278a8f8b687d964", "guid": "bfdfe7dc352907fc980b868725387e98162e2a5689a129dba61a30062c656c93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800b52c6c0c481cf29094dd0dd2275e04", "guid": "bfdfe7dc352907fc980b868725387e9874c29c73a36beaebd65b0cb0fb9225af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892ccdaeb44785a81bb0726ebeaadb1a4", "guid": "bfdfe7dc352907fc980b868725387e98d432c3f46b28ab7c360d9d055f1a6ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bad8698439d52d4f94845f6d2eae621", "guid": "bfdfe7dc352907fc980b868725387e98a94054347508856afb9f5fd629bd1851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b696aae7f1b2bb673a816f0365d483d", "guid": "bfdfe7dc352907fc980b868725387e98eeaa4a3d7df0ff0514f95cf919a6efc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845c61d1ced5a05688dc36bc9578cc31f", "guid": "bfdfe7dc352907fc980b868725387e985b9dbcf06d35ab48c4feae2b62c0a9b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842e468f072d1dd58b88e3259a4f3f4f8", "guid": "bfdfe7dc352907fc980b868725387e98f0cb55ef7a1593e9205541db820b22c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf031164228631698e3e6bd3a447867", "guid": "bfdfe7dc352907fc980b868725387e98193b45a979ab1a1e15db8ad8f20b7024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98accf8daa386ccd8efe5b36199c150555", "guid": "bfdfe7dc352907fc980b868725387e98b33dff1b8addf2e809060b9f0431620b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d3c662f35894765fb63215a82373aef", "guid": "bfdfe7dc352907fc980b868725387e98c37a16228db23d06e86e00dd29019c33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c01d7228cd598e522227df49a1b6bae9", "guid": "bfdfe7dc352907fc980b868725387e981e2e1346a1633ce1ee4eb4cb55b57c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a372f66ba24c9aab58f67b58bbbe5ae0", "guid": "bfdfe7dc352907fc980b868725387e985d9ebef983dfa659514f8975005289dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865e03b0e3d913606f01ba02e76560509", "guid": "bfdfe7dc352907fc980b868725387e984c9519493ccdac0b918d35fb0cf523af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cefca125e4ad63d00ef49e4ebb22cd16", "guid": "bfdfe7dc352907fc980b868725387e98e80213b22df3304548a02d49a2d8e3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fd3808eaef1b0b0570d896ad3333a8c", "guid": "bfdfe7dc352907fc980b868725387e98f08ee4bfef07c27371e0706f6620feb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c640229c6344e364e55087a5fee8f7a5", "guid": "bfdfe7dc352907fc980b868725387e9875e616075cc95c8cd36fbd21fd6b0440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8a6631fbd9344fabeae01094f321ccb", "guid": "bfdfe7dc352907fc980b868725387e989658d9408f6bba8d3ca6ca5e9dbc8c90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986443a525b4316abda855214f8dc711d6", "guid": "bfdfe7dc352907fc980b868725387e981a727837450fac18a64627732dc82897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882af4ab6bb0302450211253a253da708", "guid": "bfdfe7dc352907fc980b868725387e98be4bfcca04bf0482d361a80c377ffc42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a88f1ebfbb926d3c591d9f218be22fe", "guid": "bfdfe7dc352907fc980b868725387e9885397fa3e0a6ac3fe2c9b9c7b909826c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809f13391532ecd12ed2e2c7946885bf0", "guid": "bfdfe7dc352907fc980b868725387e98d61c6522170340c11f804d594d516906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb04cefefaadf2456b451d7bc462377a", "guid": "bfdfe7dc352907fc980b868725387e984e695630728ab22705d1b42df7f49906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e08143e9f2d7ddc3f709a93a50016a1", "guid": "bfdfe7dc352907fc980b868725387e9805b0bcf4ef09ecebd888d0e223785cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cd50734771b1479a95a816cc7afb0d9", "guid": "bfdfe7dc352907fc980b868725387e98a601a1a8dda4277e4adcc83c0e68fe33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362a5e1bc976fda50efeb44f19793d48", "guid": "bfdfe7dc352907fc980b868725387e98e207a2aef02fa50a6ec65bb21c712e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818fc2f0866c5d7b88bbd6a06138c73f4", "guid": "bfdfe7dc352907fc980b868725387e98472398837d89d36c21b64cc493a94a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc8e8355d87bbd42aa26cdb117ae9cd0", "guid": "bfdfe7dc352907fc980b868725387e98e72d95288ea46299e61700c842e7b4da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f84ea2bbb339d26e49d8e7da1e4f89b", "guid": "bfdfe7dc352907fc980b868725387e98d0061c648c5ae49d828560fcae9a0534"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e81a0ce9044906feb06857bafc5d987", "guid": "bfdfe7dc352907fc980b868725387e98225b5fe7185fdcd8126bbe8d691c29ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e86c9ad8da56472bb696cc73964d548", "guid": "bfdfe7dc352907fc980b868725387e98262a5ae95bec7b8f6379fdf1257d87a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895fb4cbe71e3f5da2366259bcf6bffda", "guid": "bfdfe7dc352907fc980b868725387e98e3e492a5e14907a00207c7702af8d212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982662760aa3b4dc8d6d9af157dae5067c", "guid": "bfdfe7dc352907fc980b868725387e98cda12c54392375845b2f8302144b7245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98729ce356c28611b3981a7920f1d4fa20", "guid": "bfdfe7dc352907fc980b868725387e98af0decb17c191742bee91d1b04138c8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831f319145de346ea1837f1d1727033bb", "guid": "bfdfe7dc352907fc980b868725387e98ba174d72bbb3036585382e9428ba9077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d34a628f0177804b20b63a1bec01e835", "guid": "bfdfe7dc352907fc980b868725387e98be57fb2fa056f7111daf382f49270451"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853f3b08e47053f5492abd12ad02934cc", "guid": "bfdfe7dc352907fc980b868725387e98c14d819d66ed9375efcc6a539b85b7ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890400a0941a81dd9fb46d797acbf42c6", "guid": "bfdfe7dc352907fc980b868725387e98e308fbcee206f934ffb00475cc523a36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e07543f2518a0c1eccaa3eee9f6cd2b9", "guid": "bfdfe7dc352907fc980b868725387e98b3132a0220e94aab07f737db82c3cfe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c88036409464a9090bfda2e6b34066bd", "guid": "bfdfe7dc352907fc980b868725387e988893378490c2052a8a464aa57090531f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877b92a2ab946f73b4f0d4b8b6aad712c", "guid": "bfdfe7dc352907fc980b868725387e98154b1d25eb1a439b8767d19ef39e1ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988abaea74286f678b750a349a0b30e2b2", "guid": "bfdfe7dc352907fc980b868725387e98b0674a3442b1a2a48f3f4262ef7237b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98088c398f0fde6ea29d8a73dde4cab781", "guid": "bfdfe7dc352907fc980b868725387e98ebf7b3e19e4965170d45392782771cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b87e0f2c02e9a27447c068fd8686e228", "guid": "bfdfe7dc352907fc980b868725387e98852d58f5e479ee1b5233ae013ab7002a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7208d767d61b9d1ddee119e4c12e10b", "guid": "bfdfe7dc352907fc980b868725387e981fe38c3f705316063a59f18ad70ac314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af13ad5ea772ef7b20d4f3368bb67b3e", "guid": "bfdfe7dc352907fc980b868725387e983c25e1b3ec34195ddd1a48092e7d5c26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d163eeedbd2ea8b2db9ce1ff55ca70", "guid": "bfdfe7dc352907fc980b868725387e98a68eaafbd6e7d960af0028a8386b3330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e9acc80c151dc7fa94e61a582d6dd69", "guid": "bfdfe7dc352907fc980b868725387e983f3d27c22745d72b49edccf768470f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98212339d6616b48158e39b38be14e817a", "guid": "bfdfe7dc352907fc980b868725387e98e8f28ae119a17ae79c19b0db5ad09cbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bec9c8bead2591addf18439a11722ee", "guid": "bfdfe7dc352907fc980b868725387e98bc6a9ba2dd8868dbc2fb45beea927d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98762103ed92b61b2485fcb0aeb53a1fdd", "guid": "bfdfe7dc352907fc980b868725387e98d7d69d6c5dce8ac5c9d18c643e30de06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9843f6f884caebd89f8d747e1b8aed3", "guid": "bfdfe7dc352907fc980b868725387e98e3d136553c1a88705b7f7d040ca3a677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1209dd0e9377de9b36ff3f30e68c08a", "guid": "bfdfe7dc352907fc980b868725387e98ee9a4010577dcfb314226dce4f361e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79b2ac31dd58d6f466d34ba1b3443e7", "guid": "bfdfe7dc352907fc980b868725387e98e41af1ee7dc191c44cc159e5c2478f05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfb87448067fc68dc05c69ef6ae348fa", "guid": "bfdfe7dc352907fc980b868725387e981410763db0b310f0024e0807fdafd422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987970a6e4ab083b856fd603928841cf70", "guid": "bfdfe7dc352907fc980b868725387e98b96aa6334ad332103ecc32edb480d0f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1062bf9fc4c83e69ef6ae81b6c2fa36", "guid": "bfdfe7dc352907fc980b868725387e98693ab963407f58e1d2533433981cc8a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987afb854716ba1b62b39dc9bc90651013", "guid": "bfdfe7dc352907fc980b868725387e98530872d8b12748c7cc42dfe53eb608f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6fb04f65530bc0138d0607472327b28", "guid": "bfdfe7dc352907fc980b868725387e986550f864d69eb5b789ef74c2f45eaf60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bae934469349c1373bf9b89af923503", "guid": "bfdfe7dc352907fc980b868725387e984ca6c988a2f9d3d9b69702cd0b293872"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e4ad64772ea7c6891227e1fddd2311", "guid": "bfdfe7dc352907fc980b868725387e98c0aaaa1042ab55ac68bc3d509c3c0620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5e1a3a5acd01f412c37e90ced90d7d3", "guid": "bfdfe7dc352907fc980b868725387e9887ca8566a609060d63341628c7144b4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883f058eb819310abd73b49b00dbeab38", "guid": "bfdfe7dc352907fc980b868725387e98c83e1006198954f0fa914a878343b98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98267feeeb3df1e1e9d089c1ba6c87997e", "guid": "bfdfe7dc352907fc980b868725387e98315f7552f7ef320c380e76f948f8d0ed"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d4df7a3ce32cd2864e54d70150538a1", "guid": "bfdfe7dc352907fc980b868725387e98ec5268a1792f6b5cd4753bb67be7b719"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}