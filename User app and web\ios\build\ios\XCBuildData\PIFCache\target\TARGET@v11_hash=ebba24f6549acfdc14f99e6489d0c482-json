{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988f825c46dbd4d75f15a7ac9f5f54ae1e", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/pointer_interceptor_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "pointer_interceptor_ios", "INFOPLIST_FILE": "Target Support Files/pointer_interceptor_ios/ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "pointer_interceptor_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98533b774406b57771f5010da89159e0c5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b23ad30d138e99e802192463eb101021", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/pointer_interceptor_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "pointer_interceptor_ios", "INFOPLIST_FILE": "Target Support Files/pointer_interceptor_ios/ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "pointer_interceptor_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988b00c2986f31c807e1772a0859c38038", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b23ad30d138e99e802192463eb101021", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/pointer_interceptor_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "pointer_interceptor_ios", "INFOPLIST_FILE": "Target Support Files/pointer_interceptor_ios/ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "pointer_interceptor_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98643aa0c35809ed8b174d4a70e34efcaa", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98118f0548587c1cd5d026282466634c0b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981bf43ef8fd4ae65b33042401a527a44d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e37d591d8b0c61b767f65db090538d4e", "guid": "bfdfe7dc352907fc980b868725387e98e4f719989fa05854d522a3eb18d90b35"}], "guid": "bfdfe7dc352907fc980b868725387e98eddea7b4d982aff3a136f02f09ba8098", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e989f9161ff8e5b778c00c0f4202dcaadc9", "name": "pointer_interceptor_ios-pointer_interceptor_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f49a561b1f656669a5bf019886c6571e", "name": "pointer_interceptor_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}