{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c7f4abcf0b545a6b92ef514c58cdb56e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9859ed185bddda9fac98fb955dbede7af8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c41ba06bf1449a734b2c46fd41cc224b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad3933a0aa7d10d295b0b450348462c6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c41ba06bf1449a734b2c46fd41cc224b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9800a96723981eef88cf31d8c7236d90f0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f89648d28e78bd9cfbc3cb253a86658", "guid": "bfdfe7dc352907fc980b868725387e98c4f5c98f0da774a21e280eab7fe057a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eed9e20515f988cd62fce2d1258b1e07", "guid": "bfdfe7dc352907fc980b868725387e9839fc6c7c664204e244b373b460a9432f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f82f946a9c137b89b0869b9c9528e1ca", "guid": "bfdfe7dc352907fc980b868725387e98427a666d7183895e3731d9813f412e20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eb95840b683c34cd327eac433dc69a5", "guid": "bfdfe7dc352907fc980b868725387e98e6727dcc8f1485e917b74fd4bef0a6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be6c0a20b2ff0e35d88c528611c9c38a", "guid": "bfdfe7dc352907fc980b868725387e98df277450d913184a2fa4d6f95009ce33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a3f808f876feb9488d5cb5a6b3a836f", "guid": "bfdfe7dc352907fc980b868725387e98eb2550ce05cbbca7cd449a6c4ff75f87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989215fc9a1732b2d0f00d376ff9808f68", "guid": "bfdfe7dc352907fc980b868725387e98638198299c71ff4f47d5a92a029a6b66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb9d7a411da5f3e646f9b908ae384da0", "guid": "bfdfe7dc352907fc980b868725387e983f2105b19a45887ec0cd99b8f4098a91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817ffa73e8f9bde79187efc5fd5d1c55e", "guid": "bfdfe7dc352907fc980b868725387e98705be49b2174f3047c35324fab7fba8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea7f6870db6a76d58b0d72fc8caf700", "guid": "bfdfe7dc352907fc980b868725387e98552ebb95fb1f5d91334db314a2781a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ae4f25c0ab24bfe84f9d59bbefaf08", "guid": "bfdfe7dc352907fc980b868725387e9890339403453f254130360d2034e1ca48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed604dd43b48d11a5de31aeddac55353", "guid": "bfdfe7dc352907fc980b868725387e983a5f24f9d5e80f37cf291255e141080c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eacf12236d12d080ab072e32696add48", "guid": "bfdfe7dc352907fc980b868725387e98100c4dd103ffa81658874e2b2d85d5e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f744567fb3e40caf88e3f7538ddfca2", "guid": "bfdfe7dc352907fc980b868725387e98617f042988b16886e7cc20ec65a58165"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986720bee9b5abdd6cdf2e4923f83672dc", "guid": "bfdfe7dc352907fc980b868725387e9865b0896451f9d6cf2283a039fbf63cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891730569b70b2e65601b8b2ddc175977", "guid": "bfdfe7dc352907fc980b868725387e9817b2cd278806279c92d05559de20d64a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee03fafbdf61e6d6c1e8ef7756bb511a", "guid": "bfdfe7dc352907fc980b868725387e989387f43106c5ec26f7c28c4ccdaf7a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861fd19b7b0d137d41437437d33eca97f", "guid": "bfdfe7dc352907fc980b868725387e98a579ec35ea48344f451f640f5133a97c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b826ae964462d6581550baf681900ca", "guid": "bfdfe7dc352907fc980b868725387e98247ce5c43b725c1b4d8c5b17a0915e14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98594cf183a6313dc510a371fd711aefea", "guid": "bfdfe7dc352907fc980b868725387e988f1107f583b9dbd85850564d62dbe5ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a8d84cbe63310c2fe12e20625295a61", "guid": "bfdfe7dc352907fc980b868725387e987199af7a0850f7694381ed347c4a2089", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed24b50acc9bd662440f5ee2dfda783", "guid": "bfdfe7dc352907fc980b868725387e984c3ddb72446e455ac3be9c0c8232f685"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee718696b90f359e20d6683ea84f17ac", "guid": "bfdfe7dc352907fc980b868725387e988e030a7305ce8e1c8acf4e05f72438fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987989861325f54ec43ac08d3145911c17", "guid": "bfdfe7dc352907fc980b868725387e98a6603a3db649375f87186c6acf3277e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894d26d70211403f4f1963c120784749d", "guid": "bfdfe7dc352907fc980b868725387e984378d226f2fd781453f3a6279bdd9a5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c7615307b235e638bda28296f78153b", "guid": "bfdfe7dc352907fc980b868725387e983aa61192ae40fcb1e7747889e964445d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f408fa7ac36ab77366ff9d9be25553e", "guid": "bfdfe7dc352907fc980b868725387e98091769d6d3ea2dad8c8511b5d34270ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ace826008faf5a2dcca9b7f3a4a90b2", "guid": "bfdfe7dc352907fc980b868725387e989879c076e1ae8cea441a6b6f2a225e71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2d945d2a3238f596daf7d9de694b123", "guid": "bfdfe7dc352907fc980b868725387e98950714569cbb291ac99cde4b378f4651", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988516f9366a801e683984a668c43e38f8", "guid": "bfdfe7dc352907fc980b868725387e984a1d9ef740af0331a31f3d263123ae9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982080854a453b243d19ef01dcb2e87625", "guid": "bfdfe7dc352907fc980b868725387e985e82d340fc8c2378a4a2148d02ddaf80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b8a77dcf9725114198be10622e5ee8b", "guid": "bfdfe7dc352907fc980b868725387e980a2586542aee47f50069b4e609e95eb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989929a9a69f5d4714017d345c8f54550b", "guid": "bfdfe7dc352907fc980b868725387e98ff329000b4f20b17a27ea21ce0626977"}], "guid": "bfdfe7dc352907fc980b868725387e981aded8a02e4b1e337a315b029e4bfb91", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9862f3b78d72fb1df4b7fa768fb948633e", "guid": "bfdfe7dc352907fc980b868725387e983e6dcd61148781ca855fe92f7db09b2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4cdc9d22cf30ddf191fb9c34fc47aaf", "guid": "bfdfe7dc352907fc980b868725387e9866e5064fd7a9125a6b713372d680c524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a25c73a0310d49f7e675ca6b0d592620", "guid": "bfdfe7dc352907fc980b868725387e9872d600e0be7a46c569226c942040fa57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5ea7f6a6343c852cf23b2fbaf237888", "guid": "bfdfe7dc352907fc980b868725387e98ee427f7940a070d745623c66d8c1b97b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cf9134f2211157e93e3e7b199f7350b", "guid": "bfdfe7dc352907fc980b868725387e98808c3f107c3150d987a622f7d1bbda56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d761f03a41ea5bf0f030eed2a1295d2c", "guid": "bfdfe7dc352907fc980b868725387e9861eaa32b5adcdd7d6ee6553abcbf3d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7e5568714c8f6b91921098ba9361c15", "guid": "bfdfe7dc352907fc980b868725387e9811c8c0afe8469cb9c707bf9746a7ac2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf979d2b5d00885d05f77c28e53d3c1a", "guid": "bfdfe7dc352907fc980b868725387e9897e87d5e4218ee912acae6064cce6234"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3fe8f3f484ff77b5765d16358c91bbd", "guid": "bfdfe7dc352907fc980b868725387e98d83895c00a5d47fee98859d5c618a8d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c40c57b48b5b7b7e8b2122190b4056d", "guid": "bfdfe7dc352907fc980b868725387e987e404fabd4e635a6a708573622e6eed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988503e9559519b7d96e110213662ec007", "guid": "bfdfe7dc352907fc980b868725387e98c4f914657109c26cd7a882f091e4852b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802fd53b4c6cb464e53179726f0699eb7", "guid": "bfdfe7dc352907fc980b868725387e9855dbb60637d32e33e42287f4d9fc792c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b728267e49cffbf93377a63da62b078", "guid": "bfdfe7dc352907fc980b868725387e98158466ccbc0debc03eb00b4ca0c3afc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840e9cacee6710bf9c4329d0d52153238", "guid": "bfdfe7dc352907fc980b868725387e98801320b4a527d37dd02e224865a958b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c001bf6693d7c03874ed26209e08e2", "guid": "bfdfe7dc352907fc980b868725387e98d7099315dcf4f878890c3932418d5154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f92e4921ce3cb58251453cadd370045", "guid": "bfdfe7dc352907fc980b868725387e985966cde74b01bd2ba48d65abbebc9c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861a750c8d17f7deb72d2ffdde62ca049", "guid": "bfdfe7dc352907fc980b868725387e9828395b9104b94e746bda0055712ef66f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98341dc3deb83b2d148d18dbb1ab30f05d", "guid": "bfdfe7dc352907fc980b868725387e981f81db25bccfef163e8f58db59961417"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847fafd42454682bb100e3f629d79e736", "guid": "bfdfe7dc352907fc980b868725387e9839786429cdc16b47ba317f6fc105d1bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8bf80541307288be37a506f9e94d8", "guid": "bfdfe7dc352907fc980b868725387e9874acb33857fc8088fa06c76d5b5baaf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5cb15ccb5a06f50f6b7bf3c967b5e58", "guid": "bfdfe7dc352907fc980b868725387e98b97d363b0575d23b26a4f5b8b703ec17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d670cca99034c6f58e45ff6e7161961", "guid": "bfdfe7dc352907fc980b868725387e985519cd16c390bfceaab7853a823fb459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986edb9b55389d085e2564ee1ded16b1a1", "guid": "bfdfe7dc352907fc980b868725387e98b36f75eed1c780aaf0f08cec0eb278b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5bb21beee62be73f8e19420d6808973", "guid": "bfdfe7dc352907fc980b868725387e98e41590211de7b295360237849f5f3e4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5f011694f2cb20019fe3b8a60c0edf5", "guid": "bfdfe7dc352907fc980b868725387e98943ae06af83ca3f01d2ab544826ef749"}], "guid": "bfdfe7dc352907fc980b868725387e98a321da4fc5869c3c238cb23e0891a680", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9888b6c3c65f5991bfcda51a9f62d2b166", "guid": "bfdfe7dc352907fc980b868725387e983171ca607709ba70b7546ebed4586225"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e862e3eb208a928b09c5f864ccd790c", "guid": "bfdfe7dc352907fc980b868725387e98c2d16e607333c752087b1e4969575d2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e985bf343262a16656a91a507b09bce39f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986912a28286737fa1318c8c4b807ccb76", "guid": "bfdfe7dc352907fc980b868725387e98130ec0c2334a400e82d3ad32be151f71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147ddd183486f54d4497ffb62c36358a", "guid": "bfdfe7dc352907fc980b868725387e98d54612bc5779951b16ff59fe9db93e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981925823dade64e954318992e61d3ff24", "guid": "bfdfe7dc352907fc980b868725387e9825424d0feb24a4df1e3c56fdb9919796"}], "guid": "bfdfe7dc352907fc980b868725387e9897accaa9157e6d4daa68cd6abfaa8465", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9892138fcdebd2c18a4559605a27420448", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98e408fa91ecb9a202b7a733d7c8b68761", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}