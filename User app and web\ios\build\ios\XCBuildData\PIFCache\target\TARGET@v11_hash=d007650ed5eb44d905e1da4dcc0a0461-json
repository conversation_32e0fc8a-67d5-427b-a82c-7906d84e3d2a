{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860cc83753b8573c9b6d1ce739ec05c65", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a463dc8b7efd10f79a2f6ee4cb401d07", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888f7b98752735403a5f52e6d3374bb5a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98963237e47b8ca67a149b0aa8e6c47999", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888f7b98752735403a5f52e6d3374bb5a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984bb5292d4106be88abefe8ca3d3bb3f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c0e1f5d4b3f8ff5723f8d377554601d0", "guid": "bfdfe7dc352907fc980b868725387e984b4f7b1d333d50f569d759bd0c605074", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983a46d2e85ae948db12768f62efee391d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980702b772d11144c28f26d972c6bad7f0", "guid": "bfdfe7dc352907fc980b868725387e986916c20156444af3046fe7f1e8106f84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988992fb59f5ee4a57c8a1d29d1e42d02f", "guid": "bfdfe7dc352907fc980b868725387e989e53fd7940ac3d2b4699e0a4fbd778bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae9cebd08445ada55fe36c6316cfa4b3", "guid": "bfdfe7dc352907fc980b868725387e981dac25a4d35366098f74e68b81e2b7e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb08ac117dc1dda0ce08c5724183efb3", "guid": "bfdfe7dc352907fc980b868725387e98214ece478f71de910d75c685f3d999b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d6ba54ca1b47ca110bb2a2bc160176", "guid": "bfdfe7dc352907fc980b868725387e988c145cd10897483a90e8b3ea63e71b03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc5105997a750a3d87d8f66ee6176a3", "guid": "bfdfe7dc352907fc980b868725387e98af4206f5c620c5e42ced18c00d8c0b33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc1214f31bd04fbf0b18f8ebe8f98d70", "guid": "bfdfe7dc352907fc980b868725387e9859aaabf6cdf0ae339e26f918796aabc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc87a98b19d93a165a356f8f727fe15d", "guid": "bfdfe7dc352907fc980b868725387e98556f42b085e0a5a1f03f55ee7049bc95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b24f2b72fe08e4ff1a31bcbf87c8f5a", "guid": "bfdfe7dc352907fc980b868725387e98a43d1c2e1ba849f76c65a91bfd796f6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a4763a2b9fe77492150611e5e5cdd22", "guid": "bfdfe7dc352907fc980b868725387e985e21b2ccd7c1b00328a9a91d8b596c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bfd11c8a47807eafffd2132e462e241", "guid": "bfdfe7dc352907fc980b868725387e98377b87ab832135a1bb39001f9d74bdc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b800df8e25d8707c1292e4bfe54abac2", "guid": "bfdfe7dc352907fc980b868725387e982c7c23fae6c7e03ce2d84bb07ba28606"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98239ab646b0cbfeed300f5ac879a2feea", "guid": "bfdfe7dc352907fc980b868725387e98fbe6102f68ed909b7b396248021a2b33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983226ce005b90308c6fd201e6f8a04ac1", "guid": "bfdfe7dc352907fc980b868725387e98af21f6ab0be1254d008e6e4d836623b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d633217a8105ff1811dc72e8300f6535", "guid": "bfdfe7dc352907fc980b868725387e98e46f30e0c555b251fcb94b9ea734e13c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c3a76d8096b9664ceebeb802f10f691", "guid": "bfdfe7dc352907fc980b868725387e9876dae26fe9444c56a4eae67a0113c4fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed694024c46fd7fbe823a4c55ea5b0b", "guid": "bfdfe7dc352907fc980b868725387e985cce32f719fdbfa3e6021d00072a4716"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985528ed96efd4ea49e737267de0b9fa03", "guid": "bfdfe7dc352907fc980b868725387e987c6501630e5d00dbb06b4aa842719a2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d6057f07194c6ca1f1debb352594b17", "guid": "bfdfe7dc352907fc980b868725387e98867dda8d8d528c6bd118ea62dae437f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98384c67b0fbe9066b8f3adeed3b344d05", "guid": "bfdfe7dc352907fc980b868725387e981610cf02c980df1e3369c9aa87af03b9"}], "guid": "bfdfe7dc352907fc980b868725387e9847d290be4f04a72eb32150f3ee496bd8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e9881c1fe84c8cb353e9cefef09d1bf4ca4"}], "guid": "bfdfe7dc352907fc980b868725387e982a75cd10bbb6ce086af4e09ac6511f3c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980793de37967a6f08e2a496c9e4f7895a", "targetReference": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3"}], "guid": "bfdfe7dc352907fc980b868725387e98f3b98a17049b0ca2a766b8eb576d9ce8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "Promises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}