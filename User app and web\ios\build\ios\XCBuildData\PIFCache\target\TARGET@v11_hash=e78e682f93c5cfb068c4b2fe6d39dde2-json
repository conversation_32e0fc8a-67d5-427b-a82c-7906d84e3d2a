{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981dfa7645ee37979fc678c7115e386773", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d15420b18834da786f148242969989e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e669b3071c34625f295a2aa44e7ed7a6", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acf95ee3ab2a6ee729287c0d42603188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e669b3071c34625f295a2aa44e7ed7a6", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68d75e79f98bc6640329a44a26f353a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a7c27af49921547c1b91f41d84e1595", "guid": "bfdfe7dc352907fc980b868725387e987b2a96482406cd48d3c5e964dbef8bc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef5d5549bd5bd043aa1db67f7d98d9fc", "guid": "bfdfe7dc352907fc980b868725387e9834bcd61548e1006692ffca0df79d05ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7a58d867c501d1d62e0e7e6e94a939a", "guid": "bfdfe7dc352907fc980b868725387e9826d0a88b076166c4b864cdbdbce92bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c67567291a14817a2549d34ca363fbb", "guid": "bfdfe7dc352907fc980b868725387e98f06dd451ac8f1a44c57764e553b527a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2239810ae822926e82d7e1f4ff8c56", "guid": "bfdfe7dc352907fc980b868725387e9858166e44940ebd93c8b5a766b12b84f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e248088f8d208c58299725157cfc7628", "guid": "bfdfe7dc352907fc980b868725387e985bf0c4cd98e92817abbdcc35bdc5531f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98999fcf197f3c5efbb6a58664f8151658", "guid": "bfdfe7dc352907fc980b868725387e98c51f26b87df945a97112699464d5bdf0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869b79cce4ef1f18cbc6c8ba1e4425cef", "guid": "bfdfe7dc352907fc980b868725387e98af4c2ceadace42a2f4adc0d63da70812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dc16f0e116bf89336c2774b3365cf49", "guid": "bfdfe7dc352907fc980b868725387e986dc9311543863ced0c90776181c9ea38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b87bdc942a3a49603acc77e14211f732", "guid": "bfdfe7dc352907fc980b868725387e982c85902d07d31bb1e5ed529b4fec6022", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c726611d3af3482c56562fa2bb9d02c2", "guid": "bfdfe7dc352907fc980b868725387e98a00ffc876eed62fce6ba5794f8479b12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b440704aa35f2d522e43febfaecf33", "guid": "bfdfe7dc352907fc980b868725387e983ad80b28dc127b28a95d9b21b3eba752", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3b9369f71795b5594db160ebb88c160", "guid": "bfdfe7dc352907fc980b868725387e989e97ad7faf44c970ff1e6892a1c6ff4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e1c808976014adbb93a5b7fa335fc03", "guid": "bfdfe7dc352907fc980b868725387e986c3f6d4e621a705b01b32cad0135ff32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4ba5b255dcc6806d22e60e6341b676f", "guid": "bfdfe7dc352907fc980b868725387e98908859acf0314667d813df278ebc7971", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df5cbcfae5d5ed390b95427ed1ab14c6", "guid": "bfdfe7dc352907fc980b868725387e9835060e3266401f00c0e55803e560ef3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfd6fcb37c7dd1ce0ba842675e6ec390", "guid": "bfdfe7dc352907fc980b868725387e9892c65e1f99e88c517940337dea3b8c13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc123a86af71978af0499772c6150a07", "guid": "bfdfe7dc352907fc980b868725387e9870e3688111116f66aea186a02ca7fedc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c39edcbd1e5febbe2551176567f6e0d", "guid": "bfdfe7dc352907fc980b868725387e98d8787100502fa9502542bcd3a823833c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982740087c6ba3aa6b8e63a36056636ced", "guid": "bfdfe7dc352907fc980b868725387e988052de86f70283058d324ddc6ab5acdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd2bb66d48a67aed149f645b9b897cad", "guid": "bfdfe7dc352907fc980b868725387e9872a4c5a4082445e692c9d19c755c8ca6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd88aba6a11fab7c981b218d972e2ec0", "guid": "bfdfe7dc352907fc980b868725387e9804b492d3ba5a3c526fd4ef87a8c1332c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98905f02bbf23803adb5fb6e9a67f23612", "guid": "bfdfe7dc352907fc980b868725387e98a6052558bee4cf5128d3d0f542c926ed", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c8c539c8b21555bcb4eb3367cc806c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980af7abe02c763ddb2a3a4eed68c609fc", "guid": "bfdfe7dc352907fc980b868725387e9849d0047527a358bfff8af909b731bda0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a01866df23a65d2cf76b2bc81f6118f", "guid": "bfdfe7dc352907fc980b868725387e98058b771dd892aad80c349da616705ad7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b0af707bd00c6d985a5a3fafa9a4536", "guid": "bfdfe7dc352907fc980b868725387e98a33accb89e3fe72aed974b9e3f2fa683"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edeaf76442677ebd61a0f00fb06764a6", "guid": "bfdfe7dc352907fc980b868725387e98531d786c9c0863ff8e634db7caba945e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f59fac546042f3a86970f464ba53875e", "guid": "bfdfe7dc352907fc980b868725387e9809daffbf8d209e4827b067f350b66ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b698fc31cc87b1afa4483bc6b266677e", "guid": "bfdfe7dc352907fc980b868725387e981ec6e69e78436e9f90db8be66c2da384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823cd3bcbd89cb8d9ecd2c0d98c3422a8", "guid": "bfdfe7dc352907fc980b868725387e98e87b27bbc76fcba88e2da7f95c269456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca6a3ea8193c0266c0b4509398e6e84", "guid": "bfdfe7dc352907fc980b868725387e9898a2705ad2089817ede15eeeeacfb22f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860028d0c6fe0fb97b1009714435fb8d7", "guid": "bfdfe7dc352907fc980b868725387e987b1fe7dcbc4de956ab960b813043de76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c81156ba4ad75a52c990b52c03dc1f5", "guid": "bfdfe7dc352907fc980b868725387e985e6b60e54a68d747cff302ba76b28f72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98842f2befb9545303448eb974e5129c86", "guid": "bfdfe7dc352907fc980b868725387e98b02270ba1701e242ede905bb791e4bb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800e7dd138a7e95b6e0842429cbf9687f", "guid": "bfdfe7dc352907fc980b868725387e98a5300e0a36e0f37f36453390299cc559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ce0f7c168c077a87dd664617bb5230c", "guid": "bfdfe7dc352907fc980b868725387e98c0229d70a6a984dd4b305a2b34d61e96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839165ed71bd12c9c4c78f7bc262147c4", "guid": "bfdfe7dc352907fc980b868725387e98187ffa737b89431fd12596f736d5b0ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c342d0a3028d1688a60aa31138b850", "guid": "bfdfe7dc352907fc980b868725387e98df7b7f5210ab096b5b90dc28d6a1ff40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd84049367fbe5a49b574461dbff352", "guid": "bfdfe7dc352907fc980b868725387e983cfef307d010a7dce17c978e0e3a8387"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaf63804e5c5e1280a5a186420195b7d", "guid": "bfdfe7dc352907fc980b868725387e98e02cbf019d1e54682905237fda670b58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694a9bd0878b7d46e054bcffd92db125", "guid": "bfdfe7dc352907fc980b868725387e98a58a723c82c2b84fd623236bed4e346b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983abb6c958b9c3a5892f8260e9b7a7c1d", "guid": "bfdfe7dc352907fc980b868725387e986f4b52efa0acd85e7ad08118a848fa36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98101cb410216bc84ab1b55da3b1221b12", "guid": "bfdfe7dc352907fc980b868725387e98fe02c6ba8bbe97e0b7513989d619e5b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822eaa0e52f5b599940f2e59fddf200a3", "guid": "bfdfe7dc352907fc980b868725387e984f1bf9fec22522dbec969a28b45a6acc"}], "guid": "bfdfe7dc352907fc980b868725387e98057eb75c841554d25f0f5035104e0263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e980017a4fef110a270efddb901b92f2e25"}], "guid": "bfdfe7dc352907fc980b868725387e9810423e99f15760aeac349670a9357a85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9862935e1388b76505ac12d06927e9652c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}