{"buildFiles": ["/Users/<USER>/Desktop/flutter/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/Demandium-User-App/android/app/.cxx/RelWithDebInfo/39102w3r/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/Demandium-User-App/android/app/.cxx/RelWithDebInfo/39102w3r/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}