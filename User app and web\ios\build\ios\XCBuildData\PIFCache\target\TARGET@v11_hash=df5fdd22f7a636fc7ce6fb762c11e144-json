{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987fe342e72243c7830ca4ff5d811a90d2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a9186989285bd998fb972ce277f4838", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894baf41c1d26fee5761309ba580669f0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b37a1f6412452e7499975f0eaf21275e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894baf41c1d26fee5761309ba580669f0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ac2174ea8e92e04cafb435524caf8e4e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9808591d10782876256ed51786c6d4ca60", "guid": "bfdfe7dc352907fc980b868725387e98408e096957263a5dd2d57544ac81f90a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bcd929667c862dc256a35e441628e92", "guid": "bfdfe7dc352907fc980b868725387e98689e8f5d9d65ad21e5b61ec30f1f39dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ea36148906c03a94938d9dc5ca9da1a", "guid": "bfdfe7dc352907fc980b868725387e98e496c19c18612182233ceac967d55125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca9146d583afd5dad3cf8fb9d1cbe7a", "guid": "bfdfe7dc352907fc980b868725387e98e88385533b65402ab909295f8d8bf483", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f52ac435cf8c6ada7555e10a23ba1ab6", "guid": "bfdfe7dc352907fc980b868725387e9804394cb5e1d579817d12669031158fcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815cf4a62452fafb7df82536cd9342a84", "guid": "bfdfe7dc352907fc980b868725387e98534ce7ae30363a6aeb511bc43f68b82d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f28c787e8e5e94932fdd4e3b664950f7", "guid": "bfdfe7dc352907fc980b868725387e98e193f510114a0ed25adbdb086812d328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98592ac08ce14e355719fa82ed9269c643", "guid": "bfdfe7dc352907fc980b868725387e98806918b911a08c1e453bb6f8ec6b8e5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98047699d53475b9a3ea4c786b4fbcde9f", "guid": "bfdfe7dc352907fc980b868725387e984a99f8973bf06fd7ecd48f47ea4d7b5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb2bc13ae62a9951e5afd04e4abf8852", "guid": "bfdfe7dc352907fc980b868725387e9865ab1f93c3e5dd8b94e8bedd8d50d57c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8e6271a8f0b042b6a2b130e541c0c10", "guid": "bfdfe7dc352907fc980b868725387e988b2ae869c841d48f7621a75452f333a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d27de2d043d32c90ef0dac457d676a0", "guid": "bfdfe7dc352907fc980b868725387e988f34148a1805194b0e49f7beb7274ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b34f2ca79c71050457273b4ba58fdea", "guid": "bfdfe7dc352907fc980b868725387e98ce08dc527a765043bcb0e25778b28a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834caf8ecae68ddf273a7db92438989d7", "guid": "bfdfe7dc352907fc980b868725387e98a842eade1e03a1eacccbb6112e601f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1b34776d6e8ccfd9bc978a298e3f7b4", "guid": "bfdfe7dc352907fc980b868725387e98ec400b99e867ead36fb85a9ddc0dea3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dcf956c6adfd3397a0d1c4cf00a548a", "guid": "bfdfe7dc352907fc980b868725387e98670eb1e671f51e4c05821918eb010481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b66818ba1bd4740bb99bb3552a454efc", "guid": "bfdfe7dc352907fc980b868725387e98381389c6c65e012bc3f84136e47d6551", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc30c5a92d1852bb3f03f0b689858a4b", "guid": "bfdfe7dc352907fc980b868725387e98890af04b86b386ff1c6ed75d7a84b324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a7cd80f974d8315bcbde239c0e45963", "guid": "bfdfe7dc352907fc980b868725387e98eafe250e158a9866c042425cfe724ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d78c4ea407ff30e80d9e05cb98dec34", "guid": "bfdfe7dc352907fc980b868725387e98461b232e8ab9cb8320153ac40cf18b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98158eb03bde2b1083a03538a3cd85fff3", "guid": "bfdfe7dc352907fc980b868725387e9898ff37f713170e9552f08fa30531b46c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8f82540f1af7f269f7404254dc04cc3", "guid": "bfdfe7dc352907fc980b868725387e986835b8796a4c055252a4fffbf1d9c89e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceca26c96b676d5a286c70d940d9a16b", "guid": "bfdfe7dc352907fc980b868725387e988cfb0d6febc566948737750f5dd9d5d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc56b4089c2beebc0b4fcf9b63720791", "guid": "bfdfe7dc352907fc980b868725387e98d0ed8d0f8eb8a337aaf65c9f90d461a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989434e77c380ce5e277da265e4efde16a", "guid": "bfdfe7dc352907fc980b868725387e98138d4f541ae5519074e993023016073f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faba22e270af3d41dacc0e6c5b7f9bca", "guid": "bfdfe7dc352907fc980b868725387e98776136c9ef87dff6a0c1e949f8c38ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866f98e6b430224263d0a3b2ad0a5a063", "guid": "bfdfe7dc352907fc980b868725387e98f8eb522b67fbb3d7943718519bbd4bde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8f92565a5252d2cd2b8bf51114a9577", "guid": "bfdfe7dc352907fc980b868725387e98d13bb27cd7ed169779d20b8c5f0b74bc"}], "guid": "bfdfe7dc352907fc980b868725387e98808e660503e89e55ed3cf0726d416c64", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9838184cc47c1d1691ace243381cc73c42", "guid": "bfdfe7dc352907fc980b868725387e9813289d7416f058b8bc83a87b0c859a7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987483a2d292034342aeae265dd9e0dedf", "guid": "bfdfe7dc352907fc980b868725387e98a9ad811653e6da9c8d58d9707852e492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98918e3a8099d317e8eb73bfcb06e72d60", "guid": "bfdfe7dc352907fc980b868725387e98b9d8f09917866faa3437e3f849511ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899333eefb1defe307d715941caf719a3", "guid": "bfdfe7dc352907fc980b868725387e988a4806d5c4d243010b2a2ca456edc3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a97ac8af6169ba19fbda4047f04fdb9c", "guid": "bfdfe7dc352907fc980b868725387e9875c77e2f0223b6a6cb60562c80152dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b28dbb1e7f65740c06cc2ac104299409", "guid": "bfdfe7dc352907fc980b868725387e98f41dec7891271341fc51ca9cd9b3de7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed862ce9fbf2ecfa372be73014672ef8", "guid": "bfdfe7dc352907fc980b868725387e981158f7a402e3cf0e924b5ad2ccf21cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b7fa1e522b3f1cc59af861adfbe10af", "guid": "bfdfe7dc352907fc980b868725387e988b25285695893c413044e3634fd52702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893685994b46fc6a5987172c120ee6b5c", "guid": "bfdfe7dc352907fc980b868725387e98d4824ec9d4112954acb01a6b3f2608f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e0118af4cb97228abef178492e7401", "guid": "bfdfe7dc352907fc980b868725387e98eeb59f21666a8287935a608091e475ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ba22776d5429ca1b35af376388ed4e", "guid": "bfdfe7dc352907fc980b868725387e98dd7346778ce40a9d9eff6785a27f08c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a082804721b35e813f1fc8b835b282a5", "guid": "bfdfe7dc352907fc980b868725387e980de7382e22de2367c8a8ff9283195972"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837c75bcbd463f09d16a6bf41dafbe797", "guid": "bfdfe7dc352907fc980b868725387e98eb9aab8515725e70bf47b290f2ffbf9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984de256cf956300fec593853523c294c4", "guid": "bfdfe7dc352907fc980b868725387e9887170e2e2f0e1231e8f66fc27d5d75a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf4b273aa1520c61dce0c2149e56840", "guid": "bfdfe7dc352907fc980b868725387e9895f19af6f6285e65ba1a6fc39d8d33fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b0dbf9811edd5162b856d54de188b7c", "guid": "bfdfe7dc352907fc980b868725387e98151e8f2dcd937577d9c166ff24e38103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877e1c9928dc2f0a61ad9eff12aea5629", "guid": "bfdfe7dc352907fc980b868725387e98adfcc487798a61542fe811b996202b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981288447d3bb929ffb2142bdae84336a0", "guid": "bfdfe7dc352907fc980b868725387e98bfc667348db266d8236ddf09fe3f687c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e09d207b01abfac1e2d5904af14291b9", "guid": "bfdfe7dc352907fc980b868725387e989bf3642b2c1c77a0defaabcbb23f9bd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985156b382f5816198194de31a7f0d89dc", "guid": "bfdfe7dc352907fc980b868725387e9814ca939d7ef14a75c492b565d9ff81d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d640f1a64889cd2589aeced84c20f02", "guid": "bfdfe7dc352907fc980b868725387e98bcea5a5e9b0795d87ad828228aae02c4"}], "guid": "bfdfe7dc352907fc980b868725387e98af7c298017aa361478f3e0825042a7ea", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b62c132d059c484afeaf6508efc99fb4", "guid": "bfdfe7dc352907fc980b868725387e98eaea5eaf3300cd8b30cd03886bb61628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889fdb5714304e0036b889494a8170c6a", "guid": "bfdfe7dc352907fc980b868725387e989f4403d826ead7e7553d2be609e28404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e98e4ac94164d6b5fc457d5f175a5efda42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982baa291e3a1305a7e6aaf6948601edaa", "guid": "bfdfe7dc352907fc980b868725387e98342fc37c026beffd967da85b92328ff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879361b2ec1c99c8df06dcafa1900307d", "guid": "bfdfe7dc352907fc980b868725387e9877f340a3cd66ec724d38f2560ed29b57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a786142c4de84e40fbaca86aaf3a5af", "guid": "bfdfe7dc352907fc980b868725387e98d6420868497ac30ec74b165bada2ea66"}], "guid": "bfdfe7dc352907fc980b868725387e98fef37a92c516d72d02a173ef676fb740", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fbb22bb0f5869f65ac700b94ebefa304", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98a8d455f06110aff21c29e94460d34072", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}