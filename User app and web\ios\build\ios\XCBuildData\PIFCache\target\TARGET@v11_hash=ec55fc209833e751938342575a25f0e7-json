{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895e10e7fcee41262eb6053009361a336", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9854dceee6c25714ae03eeb9e52cc1b307", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9877f3d6ee0bad499987a9b5d9cd86b517", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beb40057469fa465637bd5ef85684cf4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9877f3d6ee0bad499987a9b5d9cd86b517", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9838b37c3ee0b6faea779add9da0b850be", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98390e89e6584275238759495a609694d4", "guid": "bfdfe7dc352907fc980b868725387e98f9b6005c4525b7f4aa9627998c095b22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ba496f65d9cd82bf27d4c9f022a1c17", "guid": "bfdfe7dc352907fc980b868725387e98f6a38b2c68ac29f3f8c74ae72d95351c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a930bab8bd670b4d15e9d52e5561f941", "guid": "bfdfe7dc352907fc980b868725387e98a1764ef1a52b1ba08871b839d482f4de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dec071e3132aa38f33e2299222ec532", "guid": "bfdfe7dc352907fc980b868725387e9823ea0c2844e78c86987b477da515f528"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987329a8cf753ac20b70cf58389b9a1ed5", "guid": "bfdfe7dc352907fc980b868725387e986402f427079a59d99397ffa92f818e57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989011cd4801772a1cfbf1f2b5c05d818d", "guid": "bfdfe7dc352907fc980b868725387e98f0d1f2dc0a5045851db6341cc0af108e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672480e931510b594ed1ba4f9a35f97e", "guid": "bfdfe7dc352907fc980b868725387e98ad66e52a8021f31e61cda3e06daa4639"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822449f1240c33844dfe0a5ea4b91115b", "guid": "bfdfe7dc352907fc980b868725387e987e9dc15572094239684824fcb0f0ab25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a3612e4cb763db27cb270d1d367c035", "guid": "bfdfe7dc352907fc980b868725387e9888eab5ecd4c7b9eaf656b0121477a0e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6571af085978c82fd342bc57e31b5c7", "guid": "bfdfe7dc352907fc980b868725387e98b6b78fb181cc0224f917f5c4624e3b24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98134a88611a33a4e45972b63dd584e3c3", "guid": "bfdfe7dc352907fc980b868725387e988560ec92f3cfc0344cf6185b691de41a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982055af62ec73dd0f94855d82a624b192", "guid": "bfdfe7dc352907fc980b868725387e98adcbf66e70a3e23c6ab852b090d3dab4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfe9f412082eb96598875d95428721c6", "guid": "bfdfe7dc352907fc980b868725387e98578f7a7ae780c90078aa3a5fcdb84503"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c10ee65a383a3d843e768c3ad83f54a3", "guid": "bfdfe7dc352907fc980b868725387e9825365718d171307bd255bd0f1c47a3fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98192e1b61c2822728e0fa7d42a4aa56c2", "guid": "bfdfe7dc352907fc980b868725387e98992906b5968288d33870eb51c9317d67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d881d681917505b3ceb49de1d09e4f", "guid": "bfdfe7dc352907fc980b868725387e986779fa71b367f2614ef24fb6f229b258"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eab6988d9cb0ca702db8a4db7f2bbb03", "guid": "bfdfe7dc352907fc980b868725387e98c8d05e2047ead05e90dfa309d754d14f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d928653df5227c3314b27da43bb19f", "guid": "bfdfe7dc352907fc980b868725387e98c72d018786aab8fafadaed85911abeec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac6939db3cff59acc5fcca1f0577cb38", "guid": "bfdfe7dc352907fc980b868725387e98cbc230df95c7763a678f8d22bdadc33c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d18390c71f74698a89577f1e62937bfd", "guid": "bfdfe7dc352907fc980b868725387e98098aa1742102a139ea37aa4db0f63ecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98256194547b6eeab27ef148f2a6ec1e70", "guid": "bfdfe7dc352907fc980b868725387e98eda6fed9b31483de1835a00d35c58e6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988da6199c005fb7a2964d978551c6f8a7", "guid": "bfdfe7dc352907fc980b868725387e9823addc3638d2567e538cfbd9a8a10605"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c6bea70edbc30161bdb2be4f0faaa5", "guid": "bfdfe7dc352907fc980b868725387e9836210a4567343c148aed902c7f9a98fb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9826ee655546013cc293140a077b5025cb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d51c791545dd2ce7de4edb4d70066fc2", "guid": "bfdfe7dc352907fc980b868725387e9826a5e1ddd7e6a207ef79e0b5e02a540f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849382f80a99d3333fdac0d330b9409ee", "guid": "bfdfe7dc352907fc980b868725387e98e1f69028892f46ab3d3cee11fba8b4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98678eb3204a750b785564cd147936604c", "guid": "bfdfe7dc352907fc980b868725387e98a328c48f5c2dc04dcdac3a24688f397d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c44433298d0c63ef909746c7ba376a1", "guid": "bfdfe7dc352907fc980b868725387e98d63eb776d6c1fd2df469581b2ae24ba5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ced5213e363b155dd1faacc55d7ed7e", "guid": "bfdfe7dc352907fc980b868725387e987c968687eb8ced508d420398aed2f2fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b911dea0ddfff5b30f6f4cf0dc452645", "guid": "bfdfe7dc352907fc980b868725387e98031beba65d807d9c154d1618eee39c64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e979df05a62a2de2f55251b6830280d", "guid": "bfdfe7dc352907fc980b868725387e9857282e3c37ca4eb770567545d873c1e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2ce3397403656e854dd0752ecb20503", "guid": "bfdfe7dc352907fc980b868725387e981f6523aa8cac3e71abaaecf2cdd3aa0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfa8618ff74d9ed6ddf25133eaf72606", "guid": "bfdfe7dc352907fc980b868725387e983669121a8887f1d3492f85def8f1d0aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a83649ea7d10fb546a56bf1b5503221f", "guid": "bfdfe7dc352907fc980b868725387e9822dde9efd43c7299ad941d5218fa6ea1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98958c2a61bf7f9a6636d23edcd29de056", "guid": "bfdfe7dc352907fc980b868725387e98727c9c0b5308fe8657c3b9a3ab7938d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b5357c36d0146610988691e485cb1e4", "guid": "bfdfe7dc352907fc980b868725387e98b01f734d3c7f4064e27871bc75ba16bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fac83c4ee8120205b8a55532c973d158", "guid": "bfdfe7dc352907fc980b868725387e989d2017cf85ec898071dad8e03bcf4bb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829272921d71ef6d91dfe29106dc68900", "guid": "bfdfe7dc352907fc980b868725387e980f3fe98d15cb39661e019d46222a30c0"}], "guid": "bfdfe7dc352907fc980b868725387e985891797a03af15b95b56bddf355ee365", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e986f808293fbed284fc9efa981315523cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981925823dade64e954318992e61d3ff24", "guid": "bfdfe7dc352907fc980b868725387e98fd35bef299219b61c9b53a0fa607ef63"}], "guid": "bfdfe7dc352907fc980b868725387e987934b827b664fc671e71d07e60dd431e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984a5f87ca85a3e9e7a1e9078088d2de5c", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9835ab292feac68776bafaf0c3c39a912e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}