{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b8f50fc411df215f436a516c3848760", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a9186989285bd998fb972ce277f4838", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fe71dbe0cafebb05fc2d13a1525864ad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b37a1f6412452e7499975f0eaf21275e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fe71dbe0cafebb05fc2d13a1525864ad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ac2174ea8e92e04cafb435524caf8e4e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a07abce63a2a2577c04715393b3a1e8d", "guid": "bfdfe7dc352907fc980b868725387e98408e096957263a5dd2d57544ac81f90a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b7c6a272139cead283e507df021b09e", "guid": "bfdfe7dc352907fc980b868725387e98689e8f5d9d65ad21e5b61ec30f1f39dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983190175ebc43dbde695e93ba16e73fab", "guid": "bfdfe7dc352907fc980b868725387e98e496c19c18612182233ceac967d55125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980840519283d11dac1ac6ffc36880879d", "guid": "bfdfe7dc352907fc980b868725387e98e88385533b65402ab909295f8d8bf483", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e947021d9633e8ce1cfca60aac5bbf0", "guid": "bfdfe7dc352907fc980b868725387e9804394cb5e1d579817d12669031158fcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989de47e439e35743bd4042a3633f2cdba", "guid": "bfdfe7dc352907fc980b868725387e98534ce7ae30363a6aeb511bc43f68b82d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5bf633f660a7020ceb0a7a4c0e1b813", "guid": "bfdfe7dc352907fc980b868725387e98e193f510114a0ed25adbdb086812d328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982af86bc0adec7ad62daab0be1ed528d2", "guid": "bfdfe7dc352907fc980b868725387e98806918b911a08c1e453bb6f8ec6b8e5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cf83129bdb43cb5c98c0f0575a8a139", "guid": "bfdfe7dc352907fc980b868725387e984a99f8973bf06fd7ecd48f47ea4d7b5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98089f98976501a2bdec78284c5b6f7b5a", "guid": "bfdfe7dc352907fc980b868725387e9865ab1f93c3e5dd8b94e8bedd8d50d57c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985036fd23694383aa2123dd80c8a69b82", "guid": "bfdfe7dc352907fc980b868725387e988b2ae869c841d48f7621a75452f333a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef6425d6aec92c533ec13a90b3ef66f", "guid": "bfdfe7dc352907fc980b868725387e988f34148a1805194b0e49f7beb7274ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6af5377f590cd08a108d545cdeb798", "guid": "bfdfe7dc352907fc980b868725387e98ce08dc527a765043bcb0e25778b28a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fb44f35b6c3f06fc399a95115e625f1", "guid": "bfdfe7dc352907fc980b868725387e98a842eade1e03a1eacccbb6112e601f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a49e2ade74f68c3587cacb18e9bcd84", "guid": "bfdfe7dc352907fc980b868725387e98ec400b99e867ead36fb85a9ddc0dea3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2869721a2cecd7034d365fffafa97b1", "guid": "bfdfe7dc352907fc980b868725387e98670eb1e671f51e4c05821918eb010481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb2f3861a0712811793eb853d9190dca", "guid": "bfdfe7dc352907fc980b868725387e98381389c6c65e012bc3f84136e47d6551", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc7dd937c22f634d29375db80a4e0718", "guid": "bfdfe7dc352907fc980b868725387e98890af04b86b386ff1c6ed75d7a84b324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ca884f6154139424f303b62c8da543", "guid": "bfdfe7dc352907fc980b868725387e98eafe250e158a9866c042425cfe724ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98395113ee1cf414673dd3ab7ea146023f", "guid": "bfdfe7dc352907fc980b868725387e98461b232e8ab9cb8320153ac40cf18b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878304ab66c891556a7e753d0869f9094", "guid": "bfdfe7dc352907fc980b868725387e9898ff37f713170e9552f08fa30531b46c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137ffa139be8640d1efe2ab08f3b22cb", "guid": "bfdfe7dc352907fc980b868725387e986835b8796a4c055252a4fffbf1d9c89e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845308067f3004dfa19d038c5319ff46d", "guid": "bfdfe7dc352907fc980b868725387e988cfb0d6febc566948737750f5dd9d5d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883b8c1a1dd8d12b381b71869d352bf51", "guid": "bfdfe7dc352907fc980b868725387e98d0ed8d0f8eb8a337aaf65c9f90d461a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863589e93cff64c70e31d6bf4541efa7d", "guid": "bfdfe7dc352907fc980b868725387e98138d4f541ae5519074e993023016073f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4ce204f7e6688b633336ea2b8c78287", "guid": "bfdfe7dc352907fc980b868725387e98776136c9ef87dff6a0c1e949f8c38ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986530c33074192b8f013943339f76b2f3", "guid": "bfdfe7dc352907fc980b868725387e98f8eb522b67fbb3d7943718519bbd4bde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8b6bea802ab45b6437f1176bbe1c4ad", "guid": "bfdfe7dc352907fc980b868725387e98d13bb27cd7ed169779d20b8c5f0b74bc"}], "guid": "bfdfe7dc352907fc980b868725387e98808e660503e89e55ed3cf0726d416c64", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d464d4d291c2835909657a0b9e9c7ada", "guid": "bfdfe7dc352907fc980b868725387e9813289d7416f058b8bc83a87b0c859a7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0efd461a8b3c4bae5fa640dd786b477", "guid": "bfdfe7dc352907fc980b868725387e98a9ad811653e6da9c8d58d9707852e492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af50d74175cd01fe81a6fb5bfbdd051b", "guid": "bfdfe7dc352907fc980b868725387e98b9d8f09917866faa3437e3f849511ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e37bff3e7941fe068315747960764e06", "guid": "bfdfe7dc352907fc980b868725387e988a4806d5c4d243010b2a2ca456edc3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec5feccda65e88e18b3c3bac78553ce", "guid": "bfdfe7dc352907fc980b868725387e9875c77e2f0223b6a6cb60562c80152dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c9bee8530c80ad4f5b0888d8bee5981", "guid": "bfdfe7dc352907fc980b868725387e98f41dec7891271341fc51ca9cd9b3de7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807059479d83fb285d09271dcc3b0c34d", "guid": "bfdfe7dc352907fc980b868725387e981158f7a402e3cf0e924b5ad2ccf21cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986951458e6e0372f0c55cde82884192c5", "guid": "bfdfe7dc352907fc980b868725387e988b25285695893c413044e3634fd52702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c99b37daa7f372544e025554bb42a2e", "guid": "bfdfe7dc352907fc980b868725387e98d4824ec9d4112954acb01a6b3f2608f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98713ec6f46af0b24edaa15ed0927d04c6", "guid": "bfdfe7dc352907fc980b868725387e98eeb59f21666a8287935a608091e475ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98043f1fb4c9a9d6ed92f69eaba97914f1", "guid": "bfdfe7dc352907fc980b868725387e98dd7346778ce40a9d9eff6785a27f08c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983087fef34c64536d1add4bc91ee40770", "guid": "bfdfe7dc352907fc980b868725387e980de7382e22de2367c8a8ff9283195972"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e6974c125e66b1491302453b6d6cce1", "guid": "bfdfe7dc352907fc980b868725387e98eb9aab8515725e70bf47b290f2ffbf9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ce4312736f5a03f3e04fe8fa065f1c4", "guid": "bfdfe7dc352907fc980b868725387e9887170e2e2f0e1231e8f66fc27d5d75a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b42fed0e7a4d5382e253661f78b4a69a", "guid": "bfdfe7dc352907fc980b868725387e9895f19af6f6285e65ba1a6fc39d8d33fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e442e9c5e63039ef03e139a3342e6435", "guid": "bfdfe7dc352907fc980b868725387e98151e8f2dcd937577d9c166ff24e38103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98278a5e300a900af39cf2807ee5a57da8", "guid": "bfdfe7dc352907fc980b868725387e98adfcc487798a61542fe811b996202b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806df373077b5e0e7df6f6a1b43c802a0", "guid": "bfdfe7dc352907fc980b868725387e98bfc667348db266d8236ddf09fe3f687c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890d8fb63405349ac041c2af4ebe20f02", "guid": "bfdfe7dc352907fc980b868725387e989bf3642b2c1c77a0defaabcbb23f9bd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57dde03c6814b4b62b9bbb3dd667629", "guid": "bfdfe7dc352907fc980b868725387e9814ca939d7ef14a75c492b565d9ff81d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e370931a2941728c3a2b22acda80cc", "guid": "bfdfe7dc352907fc980b868725387e98bcea5a5e9b0795d87ad828228aae02c4"}], "guid": "bfdfe7dc352907fc980b868725387e98af7c298017aa361478f3e0825042a7ea", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b62c132d059c484afeaf6508efc99fb4", "guid": "bfdfe7dc352907fc980b868725387e98eaea5eaf3300cd8b30cd03886bb61628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889fdb5714304e0036b889494a8170c6a", "guid": "bfdfe7dc352907fc980b868725387e989f4403d826ead7e7553d2be609e28404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e98e4ac94164d6b5fc457d5f175a5efda42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982baa291e3a1305a7e6aaf6948601edaa", "guid": "bfdfe7dc352907fc980b868725387e98342fc37c026beffd967da85b92328ff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879361b2ec1c99c8df06dcafa1900307d", "guid": "bfdfe7dc352907fc980b868725387e9877f340a3cd66ec724d38f2560ed29b57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a786142c4de84e40fbaca86aaf3a5af", "guid": "bfdfe7dc352907fc980b868725387e98d6420868497ac30ec74b165bada2ea66"}], "guid": "bfdfe7dc352907fc980b868725387e98fef37a92c516d72d02a173ef676fb740", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fbb22bb0f5869f65ac700b94ebefa304", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98a8d455f06110aff21c29e94460d34072", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}