{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f6f84cc37bab331730cd31fd4dbf517", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988de3cc34b46251183c583b0c6594d5b1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fbf6ab03e24fce292e856ccc8b078bd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e6cfc8980e34e4da213f168afd98c190", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fbf6ab03e24fce292e856ccc8b078bd", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e40e8e3c0cc3613d4d0d52eaeef7842e", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a7867caeea4e8419d73009459e55fba8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c62f1c9d5c7e075f541959770a5eafa7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98196538e0eb5aa0031b116f7ea6ac4716", "guid": "bfdfe7dc352907fc980b868725387e9804f844950d8c5692300f939aeb59d3d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a66e02d859f323d71bce377aa7d939f5", "guid": "bfdfe7dc352907fc980b868725387e98b4c9fd5d959619fc913975d0e04bf2e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9372748cf4e55d4180f9f693c28b66c", "guid": "bfdfe7dc352907fc980b868725387e984b0eb6910151e7ca394bddc5c38fab26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b54e5fa847a0521fce8b2334f6b58c8a", "guid": "bfdfe7dc352907fc980b868725387e982681675f5de5c3d85af0c600bfeae883"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804ed037a675fc807dec3c6911024c330", "guid": "bfdfe7dc352907fc980b868725387e98920b6b2d7acca5d2ebdec66c86fa7c6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980455fa3d42572c9d3fbeffa6e7c71ed7", "guid": "bfdfe7dc352907fc980b868725387e981b2944891cb5c6e4cb44aa31721f1dc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98520c1f3b1588b5b1afca6f2fb48d1ba9", "guid": "bfdfe7dc352907fc980b868725387e987a3a251250161c63b279b026b2a5c0ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba6e791cd3a342ef2d08d310a4e5bdb6", "guid": "bfdfe7dc352907fc980b868725387e98f1c16017c0664ca3e0aba82c7de917ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b9c92b56f4c58b3328841fe90e957d8", "guid": "bfdfe7dc352907fc980b868725387e980a7a2bfd3072425f75c66b921409c3d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98618ee2c9c8fc5dcb0f51d2a8fd0e71e3", "guid": "bfdfe7dc352907fc980b868725387e9846ce3f2adb584062a3c4a47fed006a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf529eebb1584c1d30526bec99aa1810", "guid": "bfdfe7dc352907fc980b868725387e98db7fd96396b3f3d68d839c0504c76485"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d03fcb6bdbaab221c35e8c5fb3aa481", "guid": "bfdfe7dc352907fc980b868725387e98bee466e869d65163d3ff1eedd13ac0f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98177109b46357bd8fd3d015f99a8195c7", "guid": "bfdfe7dc352907fc980b868725387e9874239a35bdbb608c3027830b377216db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc33e3aa3666b88b84600ca1b1ecfbe0", "guid": "bfdfe7dc352907fc980b868725387e989785ebfc329de3d6c3a0d7bdad990e10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a5165222106b8216dca959451f615f", "guid": "bfdfe7dc352907fc980b868725387e980b4882f47a75f3347c4466caa594fc2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd5142b980f03f1ff18c2402a84f01d8", "guid": "bfdfe7dc352907fc980b868725387e981e310a675aefd157f78b25b2feabdb31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c38df37a641a5e27de229b202572dba0", "guid": "bfdfe7dc352907fc980b868725387e9812e4f1761a02a568ddf4562230895ca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b1b57dfa0a4caab36d3d0395f49cbb7", "guid": "bfdfe7dc352907fc980b868725387e9831acfa8ef2437bd1011456a08ed79a77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f67c5fbc58d42f6794c90e8353458b4", "guid": "bfdfe7dc352907fc980b868725387e98c2a92ad52e8f7fb088f22d5480b870c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e1cd5d7b0c02af9ec01fe2e577563d", "guid": "bfdfe7dc352907fc980b868725387e987d610658c1079303695d77ecf7c4f70b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a42f9ec8e559e06d6ddd1dcfb9375e3d", "guid": "bfdfe7dc352907fc980b868725387e98b746fd48886e5c16107a13edc528dcc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaefb1e53ecae2035688982dccb454e9", "guid": "bfdfe7dc352907fc980b868725387e98dd2bb9daaca3d12955ecd7800bdaf31d"}], "guid": "bfdfe7dc352907fc980b868725387e98b003fd54f6b3436e07c1a15730c4d65c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}