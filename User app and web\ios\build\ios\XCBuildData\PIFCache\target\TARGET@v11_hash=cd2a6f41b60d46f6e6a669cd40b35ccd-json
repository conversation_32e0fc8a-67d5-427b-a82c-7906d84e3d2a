{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98979ec115520496092566587fc2bacc89", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cae96da4bd7b437f78ab61b29f0d9c58", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862a589b2f1edb975eba97e75291f1f8c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9877959f6b381445e7f55b4248514a3a86", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862a589b2f1edb975eba97e75291f1f8c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987f1a35dd874508f71206a1ba10c58223", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980fe36e0b150923665c108f000cf663af", "guid": "bfdfe7dc352907fc980b868725387e98932dcab91cd8d88769bf38682c6620d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989634458a19b3e3660f76d8fb67e97f54", "guid": "bfdfe7dc352907fc980b868725387e98617ec9212d173c6a21d9459a25f428ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98279e6aecdb097549c38efd4e51e47f59", "guid": "bfdfe7dc352907fc980b868725387e9823d2ca4bb60a5ddc41587fc090e971a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98678ba89032b1a132d66116558f1543c9", "guid": "bfdfe7dc352907fc980b868725387e98d4719942103a207ccf2fecaf22fd34cb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9873be2a229b3c11ece42cd0a0dad8ef88", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98da30581f7c104707b377faa35a7e2bfa", "guid": "bfdfe7dc352907fc980b868725387e98b1b3d71f8ae1d1cfbb40b4ad6cae8869"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3197312aedd80a43d354e9b001a1846", "guid": "bfdfe7dc352907fc980b868725387e988b54cd34478fc86dbaeaee7643122e40"}], "guid": "bfdfe7dc352907fc980b868725387e984ede4cebe2d2aea0703aa6678e560d88", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e987faaaa09c9a79f5ffd58f32a1892e5b3"}], "guid": "bfdfe7dc352907fc980b868725387e98dd75a9a7a6f83cca36b91a89e3d25378", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d8b8edc9bc301d5d2f8096fe84a9d16a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982cdb0c7c817307e018cfb4299b646a42", "name": "FirebaseAppCheckInterop.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}