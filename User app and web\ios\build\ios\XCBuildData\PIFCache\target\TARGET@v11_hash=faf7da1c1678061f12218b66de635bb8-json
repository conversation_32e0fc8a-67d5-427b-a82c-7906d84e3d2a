{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985fd437c1cc7d6647c3075dc66666ed41", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b42da1616d615b5db1c6e22c5ab5fc7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ab5cadba53d30dbef5fef54ea248dc4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a58a0b5e6f6aa8358cac5e8655fd29d0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ab5cadba53d30dbef5fef54ea248dc4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e8365beec88f84e6310a1f455d724221", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9866bfce96d8782028b16c64d58886116b", "guid": "bfdfe7dc352907fc980b868725387e98695856ef6ab04e13c50d3f355f13be82", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9867cbcfe4229e8f91c0886034dccfc003", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9880ea77003a0a2d3539c258a17b487522", "guid": "bfdfe7dc352907fc980b868725387e983c7fc62c61c680bebacdcc5256091a2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e517fa1284a418a7288f5c0ab1741f98", "guid": "bfdfe7dc352907fc980b868725387e98de71704a89b9ea437938f02ce0ea531d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837a66e7c3c0e0475e45ca38ae66ebdc4", "guid": "bfdfe7dc352907fc980b868725387e988472e840197f4408f1ec0b2430bb94c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a8ed2a5619b0d0a7e42fce958a751b1", "guid": "bfdfe7dc352907fc980b868725387e98709d9e965c5595df9267f88a70ad53d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4676b9a21932a99ac1978503088a4f5", "guid": "bfdfe7dc352907fc980b868725387e981452f8d2bc4ae347f830ec85c6df41ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811866dd978776b64d052e606c0648583", "guid": "bfdfe7dc352907fc980b868725387e98132d94f1dad191c701b38de30ff2166a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c375a46630c3790a4b7441ee06f2da0b", "guid": "bfdfe7dc352907fc980b868725387e9817b10d115f98e09f0d0a61ff5a938a62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982832fb05cef70e61e4f62080b8f3e2c3", "guid": "bfdfe7dc352907fc980b868725387e9864505da01b40d30220178b24ce13d9d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98381b75393ab6bea6434cf9e9b415eab8", "guid": "bfdfe7dc352907fc980b868725387e98c8276651ca8eb3918cd255f50ff05718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9a9105bb1b54cb7f00d00afbf4f609a", "guid": "bfdfe7dc352907fc980b868725387e983d60a041a63fe7da6557b467b73af215"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98293d862b7971b3e3716e5dc0400e98ab", "guid": "bfdfe7dc352907fc980b868725387e98d92a981b6e4195c4e1a5dc2842e6e043"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98752c74c07b71c96f7baa9a22ecf4bb9f", "guid": "bfdfe7dc352907fc980b868725387e98948c928db4bf3c31a0f998246dfe9556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e2d88f25b4319a32294150af785a65c", "guid": "bfdfe7dc352907fc980b868725387e98ba85a594454c72c5e7d91685fef7497d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a35037e99ab745ca1a76bf9372ed040", "guid": "bfdfe7dc352907fc980b868725387e985ecac94b4df11ea88d259e3f6cc44ed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9fb289cee507b05b1df85c35b32de67", "guid": "bfdfe7dc352907fc980b868725387e987c2864cd6d7f2f58aec2731470138b96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c94ab668e437452049de8b5c7f01765", "guid": "bfdfe7dc352907fc980b868725387e983eba114a1ad8a47533a9d3a48a1cc5af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98745dfa56e6e9480c5de540233ef8f69c", "guid": "bfdfe7dc352907fc980b868725387e9832629c1fc8c5e2c4b87e86fb236749f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f2904fed7be442551122577f5ca4453", "guid": "bfdfe7dc352907fc980b868725387e98457356b5b4e1cdeba57f9b5400ffc495"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986df6fbdbf88578c0a12b92fd8c898577", "guid": "bfdfe7dc352907fc980b868725387e98770dbb8cee6a0fb72344e20369b79fc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822f28971044248f71345e8a17def42c6", "guid": "bfdfe7dc352907fc980b868725387e985734499a2a26e882d664443406894175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987233406ecc60074cf2cb5b0ced0cd9da", "guid": "bfdfe7dc352907fc980b868725387e982d08aa986757c978c348ffda6bca898a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98517bee46e02a680fa718d3bd10fc9211", "guid": "bfdfe7dc352907fc980b868725387e98e8e3378a70d396749ddb21808bff8285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b99f24ed45f67ab49f99c016e0f06229", "guid": "bfdfe7dc352907fc980b868725387e98ace700ba1d3a661c67141a506cacc077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a2a74b0c45a93678fba8785d920a7f0", "guid": "bfdfe7dc352907fc980b868725387e98aec5e381669433a8e235128a5432f331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874fcfde8efdcf2c10573bf75452b6113", "guid": "bfdfe7dc352907fc980b868725387e988f25d6a0f71910a405eb56c8367e17f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820ea1784b33640dd350044014f7c5f23", "guid": "bfdfe7dc352907fc980b868725387e980866d29566e238dd093ef6b55db5614e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8582115e0730b110b5170c968cf5225", "guid": "bfdfe7dc352907fc980b868725387e98100e2114be79c7725632070cd12025c8"}], "guid": "bfdfe7dc352907fc980b868725387e986ab94f0e68b935b25db877fb3a72f2fe", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98972dbbb3741eca0374653324015c571d", "guid": "bfdfe7dc352907fc980b868725387e985b6cbec0e4d836c95ad12d1ad382463b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98614ad39d4a033dc6053f8e9e542ffd47", "guid": "bfdfe7dc352907fc980b868725387e98a6026e22e356c64f18ffc1a642c94c38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e98d8f612901b34266436b52db75b44d258"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a50bb069c5d58b85858f791831de2c78", "guid": "bfdfe7dc352907fc980b868725387e98fd2890b3f5b9d75306db544135d91be0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a786142c4de84e40fbaca86aaf3a5af", "guid": "bfdfe7dc352907fc980b868725387e9817c25832e0f87cf5877173ca98192e1f"}], "guid": "bfdfe7dc352907fc980b868725387e98bb33c651532550dc62f4264796a9ef9b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a8c151f03ae576fa6bba631193723a94", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98e1adac86a2b9ecf8b64b3b30744dfbd5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}