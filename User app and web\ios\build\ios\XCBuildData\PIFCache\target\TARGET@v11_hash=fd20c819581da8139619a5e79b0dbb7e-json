{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b7d9a03486def5e6740f4add18209cb5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ac3972ead92f97b985c645ea5a67bc6", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ac3972ead92f97b985c645ea5a67bc6", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e8ec5507af8b8c0fc8adc910ab24e1dc", "guid": "bfdfe7dc352907fc980b868725387e980eb9d23368b95b4f0923cd46a43136d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fbfd882823d97b67b36a2a4e403deab", "guid": "bfdfe7dc352907fc980b868725387e98f457cef344e960dcd5e86a14876a724f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beacdf984028970fba1d49d3403192af", "guid": "bfdfe7dc352907fc980b868725387e98448d0ae29de8f3c224c2dcfdec1456de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe9d03a6a13969065c9c8ebc6cee43a6", "guid": "bfdfe7dc352907fc980b868725387e98813b7079aa7de27e6db954b27ad09619", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98960bde0fa083e920f850d95245e200da", "guid": "bfdfe7dc352907fc980b868725387e981ea8f363f0722babcaed14c60409bf02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acfd2280b6302b98197192595ec50186", "guid": "bfdfe7dc352907fc980b868725387e98e7ac745c3ef27b02fac012aa73444821", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4e6e936e253e1c995aaf7179025f206", "guid": "bfdfe7dc352907fc980b868725387e985678ecb94e589405999c12157a408775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989beace860066338fcb48a18fe6df6999", "guid": "bfdfe7dc352907fc980b868725387e98cd87b467ebfeb07dfeb1385499e6cd77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0373d0ad43eb781ed5daa21f93420f7", "guid": "bfdfe7dc352907fc980b868725387e9871c163114fc9cc28ecff5b1b2d0a4de2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc88fc02759a57f5bb3915dc650062b", "guid": "bfdfe7dc352907fc980b868725387e981978a187afff967da6262086dc0e6fc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989269397e57b8ac36fa5b6ebfbba736ed", "guid": "bfdfe7dc352907fc980b868725387e9826c907de3610a0743fa9d834fe3a26ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f68784e3ba6ff7440ba3322d12ea47a4", "guid": "bfdfe7dc352907fc980b868725387e980aab54a36f4355218c04e0f9e0c22709", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b078ce71aee9c94b339ec7dccdfd1e", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981034a987455e488574ce43e6591d5190", "guid": "bfdfe7dc352907fc980b868725387e98d24acab5f9f5227c374c09fc08c9e695", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a7845ebee373fa65d09bb7ed6d87297", "guid": "bfdfe7dc352907fc980b868725387e98eed3f758e63ab4fdfc010074b9de804b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e66746af038d10b06e83c66ebb73ebab", "guid": "bfdfe7dc352907fc980b868725387e981f08693bd23a6d005b3df15e8e699fde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98227020bcc2848c53d5b6a2430ac7cf97", "guid": "bfdfe7dc352907fc980b868725387e98bf1362e319e49dc96debe2ff1acb9025", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e3efa2ff1dffded36c0353985dd57fe", "guid": "bfdfe7dc352907fc980b868725387e985b2090fddc5dd999ec75f69196ebf8ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98700580210f94e08de959a8eefdbe29c9", "guid": "bfdfe7dc352907fc980b868725387e98232fcc544ef53e9da9ad40243b28e5a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98598136d3826b5abb58f5e169c195e490", "guid": "bfdfe7dc352907fc980b868725387e98a63597065bd83c36166c1f6aeac7172a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff896799a06c0933e3e5fc1a3da000a", "guid": "bfdfe7dc352907fc980b868725387e9829db91ca625511289316787d6d10ea44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a492e87715c70d991a814044b457b757", "guid": "bfdfe7dc352907fc980b868725387e98c0b06c2b33df0dfa02bfa09f9932011c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810863d116d09f79d31227213e74e3c8a", "guid": "bfdfe7dc352907fc980b868725387e9851ba8fa6ddb817e605d2665626d9d641", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98738ff571d7f9f9d79d17581580f305bb", "guid": "bfdfe7dc352907fc980b868725387e98277468ca209872376e5f206f84332670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982215ffe75fff43ba6593eb9a09ce7800", "guid": "bfdfe7dc352907fc980b868725387e98e9a6f3c2565cf9a05dd3d353eaa7a4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2283b8035f9301bb203db7bdd90c4c0", "guid": "bfdfe7dc352907fc980b868725387e986111772b572857d7961a008265fc355c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98538370d5ae8cf5ddbe5ac8402f707a3d", "guid": "bfdfe7dc352907fc980b868725387e983996f9c15e1fbf682fcc40770fd256d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eda8d17393a0a98c5ec1707b715a6e0", "guid": "bfdfe7dc352907fc980b868725387e9858a8497e775e00416ce705748e767ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98379e43695a5dbdfaaea1b7746646e6b0", "guid": "bfdfe7dc352907fc980b868725387e98c6bee8141090191321d7771c3a1f978b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986771defad240c81bfd3510ca59199942", "guid": "bfdfe7dc352907fc980b868725387e981cebb2289f1bb2bf7dee62eea371ce0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1664e73eb3e141883e5a59b66cb1a88", "guid": "bfdfe7dc352907fc980b868725387e98f1f6bd36d03eee3c0bdc3bc98e83ea19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98603c42a82c79fa7e21e39aacea65652c", "guid": "bfdfe7dc352907fc980b868725387e98a32f894597a07a2a5504a8b9bcfdabdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98696b5c14a72d88bf6101e859aa40597a", "guid": "bfdfe7dc352907fc980b868725387e9800ac44543ad413e32dda38d808c2afef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98543a459791af0a0c7fa4d68933f3a5c7", "guid": "bfdfe7dc352907fc980b868725387e980a3b558904dbc56f2153ae21439d6437"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dea80b2e66bd552efe04435413bc59c", "guid": "bfdfe7dc352907fc980b868725387e9803ab6e9df07f67a9975e5c61b861ce4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802324454a4d2c981f8f9ce9e4c2778c7", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835b23ad3adb9440e1ca3d4bd2ecefa6f", "guid": "bfdfe7dc352907fc980b868725387e984a502a85d82ab7ae770f404ac3bb3b9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fac02a1d4b82c1ad859d782e64e5a003", "guid": "bfdfe7dc352907fc980b868725387e984c79dfb7fcb3ff07f9037801d36c7134"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853039babe39401114a1a45bd72d94efa", "guid": "bfdfe7dc352907fc980b868725387e98832efc148e497d7aa81cf3800da27235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985648c88c0cc848e687dbb3b69544d478", "guid": "bfdfe7dc352907fc980b868725387e988b056d871bf720cc83d9b2c7291eec07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5ce94e7cc5466252de77c94db22b487", "guid": "bfdfe7dc352907fc980b868725387e98afb0626ba205a690e15081763b4adef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ce5826219c4e59e32af0298a835639a", "guid": "bfdfe7dc352907fc980b868725387e98cf6232b3e1482ebfb128206cef66a17c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c606d1549c2a05f297812fe23c884de2", "guid": "bfdfe7dc352907fc980b868725387e98c76565777e1dccbddf6eca764d227a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ea81c8199e8975408bfe2990b50b2e", "guid": "bfdfe7dc352907fc980b868725387e98dba12151ab617cdd659014f8477b0bfb"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}