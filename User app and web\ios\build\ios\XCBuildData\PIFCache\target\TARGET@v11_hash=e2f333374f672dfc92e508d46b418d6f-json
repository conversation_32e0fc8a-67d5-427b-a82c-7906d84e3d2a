{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98812ef763369febf7f95c7a80be971c79", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9899a2a8820f9f4545faba130bba100190", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1a160d9fb69ce8d5366ec80364e49e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982f68b1c8e9713bd883fa7effbcd883c0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1a160d9fb69ce8d5366ec80364e49e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98172caa0c7e86abd796c6109d91632681", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a85c3a279c76124a31323830350107b5", "guid": "bfdfe7dc352907fc980b868725387e985ee2a367bdb1c5b295db5e823bfbce34", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9892dbfbf2e4a3557ea3bb5d5b8037e796", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d84ba300b6521c108d4dfac5361b7e75", "guid": "bfdfe7dc352907fc980b868725387e98585eb2064e6e118d224f8ee485083789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0a6d44924a41ebfa32287c551e31fb2", "guid": "bfdfe7dc352907fc980b868725387e981a10cc837d23e4084efc3d2a40f4312a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811b7f3ba1c0aa8e7ac1311f3d86409cf", "guid": "bfdfe7dc352907fc980b868725387e980b55a2c09917549ea9e3a0f611666751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d53f6408fe05bfc4cc743bd4599de7c0", "guid": "bfdfe7dc352907fc980b868725387e984b7198769465f600e29464c8ca978560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98853199a80309252797b32532b155ef40", "guid": "bfdfe7dc352907fc980b868725387e985619fc3153e2b508e1332607b5f4b889"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fbcb901b95e39373d28c4b190070045", "guid": "bfdfe7dc352907fc980b868725387e98f06e17de926c181258c28b6e76d3ad08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822c33006d829ec0620198f8500bea79e", "guid": "bfdfe7dc352907fc980b868725387e98810813adf642fda0e5c73edbc8ede36e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986faba8ac5063140443aacb559cb522cb", "guid": "bfdfe7dc352907fc980b868725387e98a6910260f09365fa21fa584da6b2f4f5"}], "guid": "bfdfe7dc352907fc980b868725387e988343f40fcd7f4d8e8798b73c10af6714", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e98465734d9dddd3b94519683fad57f2ad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879361b2ec1c99c8df06dcafa1900307d", "guid": "bfdfe7dc352907fc980b868725387e985b40cd1f827b7f9a157f054c72acf05c"}], "guid": "bfdfe7dc352907fc980b868725387e986075d94b693d9af108675649b328a684", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98548f3848f15e7ea52c6ed25ee60c8c8e", "targetReference": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3"}], "guid": "bfdfe7dc352907fc980b868725387e986eb35e750b6206866ba7365315695732", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3", "name": "GTMAppAuth-GTMAppAuth_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}], "guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98855fb84830a2ff40ce73a17fc283f650", "name": "GTMAppAuth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}