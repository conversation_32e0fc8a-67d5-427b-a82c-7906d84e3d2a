{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d21d48d90756c0141133c008573b0e49", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983acee0263ee80cf24be6e20e3fe1d937", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98586125857d8b74065901507de14851d3", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3c1f856451a44dd2238bb8e03f629a8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98586125857d8b74065901507de14851d3", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/flutter/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981496f826cc976bf124c3e2cdf403a9f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987be3966ace94d2a2468074af0cfe6f97", "guid": "bfdfe7dc352907fc980b868725387e984b4e2b2d38ceb40598d0629a6a29a998", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819de5c147e298aec8b9b7c2d59083bb6", "guid": "bfdfe7dc352907fc980b868725387e983e0f4f5b863174fcb32043e87b8e66a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2d19d856d47c5145ada1a1f8413330b", "guid": "bfdfe7dc352907fc980b868725387e98dc580796952acfb7fbf0fded35f54e67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98833deea8b68e638cbeb349fe0f260ace", "guid": "bfdfe7dc352907fc980b868725387e98d22586e69bf2aef8b039891bcc650665", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d69f115bfc5499b369c76b366a0a529f", "guid": "bfdfe7dc352907fc980b868725387e9865ebbb9e2407b80c90fed1db89b340e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba8525a786bcfa91d0b3e769e20604a3", "guid": "bfdfe7dc352907fc980b868725387e9899cdbb3f5c2a220155a76e55140322ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf455e6b1ee8ec28b3b71646b018202", "guid": "bfdfe7dc352907fc980b868725387e98fd3712aa9d01e1fc775bfabbb3600693", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f3fe39b7306b10f7cad889384942b70", "guid": "bfdfe7dc352907fc980b868725387e98850ddc5ee2cbcd07af827524c7dfb78e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a499c1a19ccd44a06269d59c39efba3a", "guid": "bfdfe7dc352907fc980b868725387e98036e607cec0f7f2b6292d3448d94d705", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9efa7db77dff4bd015561de08bcce32", "guid": "bfdfe7dc352907fc980b868725387e98fa7d18e838630224541f4a32f8fe3d79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980806b8aa75569e6a5f6d46e322e4684d", "guid": "bfdfe7dc352907fc980b868725387e98d20f516538f062edb154dff2e9e72326", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f948f7897e8118f92acd2bb46ef4de7", "guid": "bfdfe7dc352907fc980b868725387e9863adc356814f9a68bae9d10e183d3dbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98740dfb190167f39521bba5369ad05813", "guid": "bfdfe7dc352907fc980b868725387e98a562d3226230ecc9e150571e6a8ca86a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dd698082855f3354e337544936d806c", "guid": "bfdfe7dc352907fc980b868725387e986a89fa83a23fb0ba50e0aaa9890b3cb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842613268012a89003f1063434403000b", "guid": "bfdfe7dc352907fc980b868725387e98c7a4d2f624029d7e36489918cfb486d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884daff3a2658bb7fd4c929242df93c46", "guid": "bfdfe7dc352907fc980b868725387e98840c991519783c8bc41b731ef77fbf43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c38160a36ab7240a964561d2c552336e", "guid": "bfdfe7dc352907fc980b868725387e98b6ea7a37cc9b1b085cc626894408a980", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bf564ff1975ff472a8bc2c91f2bfcf9f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9805e49347d7e49efb13c0b9c443c89e05", "guid": "bfdfe7dc352907fc980b868725387e98fc2aeeff30372a8e9382bd9424da5e58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f38092c926b65782fee0295478a52b9", "guid": "bfdfe7dc352907fc980b868725387e98ba368f5b46b86ed2f9356bbb228ec697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f23a964cbae6eae8f731e46aac5e88f", "guid": "bfdfe7dc352907fc980b868725387e98a07f46006e0e0b13acf0b1712cc73130"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ca1e3a362bb72721346d414000efea9", "guid": "bfdfe7dc352907fc980b868725387e982608d167b16e3c511c909caa418e1af1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a79ab3083ff5a163f851f57d924b74c", "guid": "bfdfe7dc352907fc980b868725387e98185a926dec138453c5fabdc29d0c9a3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844d079e89299bdc8e9f2b1c9acf5ee53", "guid": "bfdfe7dc352907fc980b868725387e9881210550520b1ee3bfa30fce3d413653"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edd0153fb5afa100201a6eb59051cf2a", "guid": "bfdfe7dc352907fc980b868725387e989db863b76450c07e6a3efeefea5d239e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840fad34842dcf14390db6caa9d02255c", "guid": "bfdfe7dc352907fc980b868725387e986da211b77f3869a367c7cc59fd32c453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860da5ff832fa7a65c1f39c702999c58e", "guid": "bfdfe7dc352907fc980b868725387e981d7eb2fc908b56d891d52aed5a4a5892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdd83dd581aecafac1ad98843c76c92b", "guid": "bfdfe7dc352907fc980b868725387e98f50096c22f8337a29ee522ddda53a055"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d916b483bc693f76624286a247ab3bb0", "guid": "bfdfe7dc352907fc980b868725387e9842aab6e6820437225615089c83902796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0b94ad542b77f0b1c6f01c6ae3ea18c", "guid": "bfdfe7dc352907fc980b868725387e9857aea391777027b04b5707666b521569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c442cb6fbea3de7b02d6b037d7f47c2", "guid": "bfdfe7dc352907fc980b868725387e9881ead7d5ee06c80b6d2e153309173273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c49700e821be9f843f7a2ee9aa766e46", "guid": "bfdfe7dc352907fc980b868725387e98eaaa226cb9428e050cb7f41b2f6c2bd1"}], "guid": "bfdfe7dc352907fc980b868725387e981b2b4abaaa263ff11fa795c21ec7f9d3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7bf7ee5fbc558c481104fe48eb4be0", "guid": "bfdfe7dc352907fc980b868725387e9850046155a8d9eab9c34f139cf05eb31e"}], "guid": "bfdfe7dc352907fc980b868725387e9800faf1016cc785048ac6ced913c4cff8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc5dfdf9349a221f5258db13a60fd150", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98651b31e0272f628eba1fc39908fbac59", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}